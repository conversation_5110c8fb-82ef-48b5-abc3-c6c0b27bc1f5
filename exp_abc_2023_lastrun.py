#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
This experiment was created using PsychoPy3 Experiment Builder (v2023.2.3),
    on May 24, 2025, at 20:52
If you publish work using this script the most relevant publication is:

    <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (2019) 
        PsychoPy2: Experiments in behavior made easy Behav Res 51: 195. 
        https://doi.org/10.3758/s13428-018-01193-y

"""

import psychopy
psychopy.useVersion('2023.2.3')


# --- Import packages ---
from psychopy import locale_setup
from psychopy import prefs
from psychopy import plugins
plugins.activatePlugins()
prefs.hardware['audioLib'] = 'ptb'
prefs.hardware['audioLatencyMode'] = '0'
from psychopy import sound, gui, visual, core, data, event, logging, clock, colors, layout
from psychopy.tools import environmenttools
from psychopy.constants import (NOT_STARTED, STARTED, PLAYING, PAUSED,
                                STOPPED, FINISHED, PRESSED, RELEASED, FOREVER, priority)

import numpy as np  # whole numpy lib is available, prepend 'np.'
from numpy import (sin, cos, tan, log, log10, pi, average,
                   sqrt, std, deg2rad, rad2deg, linspace, asarray)
from numpy.random import random, randint, normal, shuffle, choice as randchoice
import os  # handy system and path functions
import sys  # to get file system encoding

import psychopy.iohub as io
from psychopy.hardware import keyboard

# --- Setup global variables (available in all functions) ---
# Ensure that relative paths start from the same directory as this script
_thisDir = os.path.dirname(os.path.abspath(__file__))
# Store info about the experiment session
psychopyVersion = '2023.2.3'
expName = '01_resting_state'  # from the Builder filename that created this script
expInfo = {
    'participant': '99',
    'session': '01',
    'date': data.getDateStr(),  # add a simple timestamp
    'expName': expName,
    'psychopyVersion': psychopyVersion,
}


def showExpInfoDlg(expInfo):
    """
    Show participant info dialog.
    Parameters
    ==========
    expInfo : dict
        Information about this experiment, created by the `setupExpInfo` function.
    
    Returns
    ==========
    dict
        Information about this experiment.
    """
    # temporarily remove keys which the dialog doesn't need to show
    poppedKeys = {
        'date': expInfo.pop('date', data.getDateStr()),
        'expName': expInfo.pop('expName', expName),
        'psychopyVersion': expInfo.pop('psychopyVersion', psychopyVersion),
    }
    # show participant info dialog
    dlg = gui.DlgFromDict(dictionary=expInfo, sortKeys=False, title=expName)
    if dlg.OK == False:
        core.quit()  # user pressed cancel
    # restore hidden keys
    expInfo.update(poppedKeys)
    # return expInfo
    return expInfo


def setupData(expInfo, dataDir=None):
    """
    Make an ExperimentHandler to handle trials and saving.
    
    Parameters
    ==========
    expInfo : dict
        Information about this experiment, created by the `setupExpInfo` function.
    dataDir : Path, str or None
        Folder to save the data to, leave as None to create a folder in the current directory.    
    Returns
    ==========
    psychopy.data.ExperimentHandler
        Handler object for this experiment, contains the data to save and information about 
        where to save it to.
    """
    
    # data file name stem = absolute path + name; later add .psyexp, .csv, .log, etc
    if dataDir is None:
        dataDir = _thisDir
    filename = u'data/%s_%s_%s' % (expInfo['participant'], expName, expInfo['date'])
    # make sure filename is relative to dataDir
    if os.path.isabs(filename):
        dataDir = os.path.commonprefix([dataDir, filename])
        filename = os.path.relpath(filename, dataDir)
    
    # an ExperimentHandler isn't essential but helps with data saving
    thisExp = data.ExperimentHandler(
        name=expName, version='',
        extraInfo=expInfo, runtimeInfo=None,
        originPath='C:\\UM MRes\\UM projects\\fNIRS_\\stimulus\\psychopy\\breathwork_tasks_2023\\_exp_20250511_abc_visual_breath\\exp_abc_2023_lastrun.py',
        savePickle=True, saveWideText=True,
        dataFileName=dataDir + os.sep + filename, sortColumns='time'
    )
    thisExp.setPriority('thisRow.t', priority.CRITICAL)
    thisExp.setPriority('expName', priority.LOW)
    # return experiment handler
    return thisExp


def setupLogging(filename):
    """
    Setup a log file and tell it what level to log at.
    
    Parameters
    ==========
    filename : str or pathlib.Path
        Filename to save log file and data files as, doesn't need an extension.
    
    Returns
    ==========
    psychopy.logging.LogFile
        Text stream to receive inputs from the logging system.
    """
    # this outputs to the screen, not a file
    logging.console.setLevel(logging.EXP)
    # save a log file for detail verbose info
    logFile = logging.LogFile(filename+'.log', level=logging.EXP)
    
    return logFile


def setupWindow(expInfo=None, win=None):
    """
    Setup the Window
    
    Parameters
    ==========
    expInfo : dict
        Information about this experiment, created by the `setupExpInfo` function.
    win : psychopy.visual.Window
        Window to setup - leave as None to create a new window.
    
    Returns
    ==========
    psychopy.visual.Window
        Window in which to run this experiment.
    """
    if win is None:
        # if not given a window to setup, make one
        win = visual.Window(
            size=[1536, 864], fullscr=True, screen=0,
            winType='pyglet', allowStencil=True,
            monitor='testMonitor', color=[0,0,0], colorSpace='rgb',
            backgroundImage='', backgroundFit='none',
            blendMode='avg', useFBO=True,
            units='height'
        )
        if expInfo is not None:
            # store frame rate of monitor if we can measure it
            expInfo['frameRate'] = win.getActualFrameRate()
    else:
        # if we have a window, just set the attributes which are safe to set
        win.color = [0,0,0]
        win.colorSpace = 'rgb'
        win.backgroundImage = ''
        win.backgroundFit = 'none'
        win.units = 'height'
    win.mouseVisible = False
    win.hideMessage()
    return win


def setupInputs(expInfo, thisExp, win):
    """
    Setup whatever inputs are available (mouse, keyboard, eyetracker, etc.)
    
    Parameters
    ==========
    expInfo : dict
        Information about this experiment, created by the `setupExpInfo` function.
    thisExp : psychopy.data.ExperimentHandler
        Handler object for this experiment, contains the data to save and information about 
        where to save it to.
    win : psychopy.visual.Window
        Window in which to run this experiment.
    Returns
    ==========
    dict
        Dictionary of input devices by name.
    """
    # --- Setup input devices ---
    inputs = {}
    ioConfig = {}
    
    # Setup iohub keyboard
    ioConfig['Keyboard'] = dict(use_keymap='psychopy')
    
    ioSession = '1'
    if 'session' in expInfo:
        ioSession = str(expInfo['session'])
    ioServer = io.launchHubServer(window=win, **ioConfig)
    eyetracker = None
    
    # create a default keyboard (e.g. to check for escape)
    defaultKeyboard = keyboard.Keyboard(backend='iohub')
    # return inputs dict
    return {
        'ioServer': ioServer,
        'defaultKeyboard': defaultKeyboard,
        'eyetracker': eyetracker,
    }

def pauseExperiment(thisExp, inputs=None, win=None, timers=[], playbackComponents=[]):
    """
    Pause this experiment, preventing the flow from advancing to the next routine until resumed.
    
    Parameters
    ==========
    thisExp : psychopy.data.ExperimentHandler
        Handler object for this experiment, contains the data to save and information about 
        where to save it to.
    inputs : dict
        Dictionary of input devices by name.
    win : psychopy.visual.Window
        Window for this experiment.
    timers : list, tuple
        List of timers to reset once pausing is finished.
    playbackComponents : list, tuple
        List of any components with a `pause` method which need to be paused.
    """
    # if we are not paused, do nothing
    if thisExp.status != PAUSED:
        return
    
    # pause any playback components
    for comp in playbackComponents:
        comp.pause()
    # prevent components from auto-drawing
    win.stashAutoDraw()
    # run a while loop while we wait to unpause
    while thisExp.status == PAUSED:
        # make sure we have a keyboard
        if inputs is None:
            inputs = {
                'defaultKeyboard': keyboard.Keyboard(backend='ioHub')
            }
        # check for quit (typically the Esc key)
        if inputs['defaultKeyboard'].getKeys(keyList=['escape']):
            endExperiment(thisExp, win=win, inputs=inputs)
        # flip the screen
        win.flip()
    # if stop was requested while paused, quit
    if thisExp.status == FINISHED:
        endExperiment(thisExp, inputs=inputs, win=win)
    # resume any playback components
    for comp in playbackComponents:
        comp.play()
    # restore auto-drawn components
    win.retrieveAutoDraw()
    # reset any timers
    for timer in timers:
        timer.reset()


def run(expInfo, thisExp, win, inputs, globalClock=None, thisSession=None):
    """
    Run the experiment flow.
    
    Parameters
    ==========
    expInfo : dict
        Information about this experiment, created by the `setupExpInfo` function.
    thisExp : psychopy.data.ExperimentHandler
        Handler object for this experiment, contains the data to save and information about 
        where to save it to.
    psychopy.visual.Window
        Window in which to run this experiment.
    inputs : dict
        Dictionary of input devices by name.
    globalClock : psychopy.core.clock.Clock or None
        Clock to get global time from - supply None to make a new one.
    thisSession : psychopy.session.Session or None
        Handle of the Session object this experiment is being run from, if any.
    """
    # mark experiment as started
    thisExp.status = STARTED
    # make sure variables created by exec are available globally
    exec = environmenttools.setExecEnvironment(globals())
    # get device handles from dict of input devices
    ioServer = inputs['ioServer']
    defaultKeyboard = inputs['defaultKeyboard']
    eyetracker = inputs['eyetracker']
    # make sure we're running in the directory for this experiment
    os.chdir(_thisDir)
    # get filename from ExperimentHandler for convenience
    filename = thisExp.dataFileName
    frameTolerance = 0.001  # how close to onset before 'same' frame
    endExpNow = False  # flag for 'escape' or other condition => quit the exp
    # get frame duration from frame rate in expInfo
    if 'frameRate' in expInfo and expInfo['frameRate'] is not None:
        frameDur = 1.0 / round(expInfo['frameRate'])
    else:
        frameDur = 1.0 / 60.0  # could not measure, so guess
    
    # Start Code - component code to be run after the window creation
    
    # --- Initialize components for Routine "Load_LSL" ---
    # Run 'Begin Experiment' code from Load_LSL
    from pylsl import StreamInfo, StreamOutlet # import required classes
    
    info = StreamInfo(name='Trigger',type='Markers', channel_count=1,
    channel_format='int32', source_id='Example') # sets variables for object info
    
    outlet = StreamOutlet(info) # initialise stream.
    
    # --- Initialize components for Routine "Experiment_Setup" ---
    # Run 'Begin Experiment' code from load_data
    import pandas as pd
    from psychopy import core, visual, event, sound
    
    # Read the Excel file
    schedule_data = pd.read_excel('participant_schedule.xlsx')
    
    # Get current participant and session numbers
    participant_num = int(expInfo['participant'])
    session_num = int(expInfo['session'])
    
    # Find the row for the current participant and session
    current_row = schedule_data[(schedule_data['participant'] == participant_num) & 
                                (schedule_data['session'] == session_num)]
    
    if not current_row.empty:
        # Get video filenames for the current participant and session
        interview_video = current_row['interview_video'].values[0]
        breathing_video = current_row['breathing_video'].values[0]
        
        # Store these in expInfo for easier access
        expInfo['interview_video'] = interview_video
        expInfo['breathing_video'] = breathing_video
    else:
        # Handle case where participant/session combination is not in the Excel file
        print(f"Warning: Participant {participant_num}, Session {session_num} not found in participant_schedule.xlsx")
        # Set default video filenames
        expInfo['interview_video'] = 'default_interview.mp4'
        expInfo['breathing_video'] = 'default_breathing.mp4'
    
    breathing_task_text = ''
    if breathing_video == 'breath_paced_breathing.mp4':
        breathing_task_text = 'Paced Breathing'
    elif breathing_video == 'breath_wim_hof.mp4':
        breathing_task_text = 'Wim Hof Breathing'
    elif breathing_video == 'breath_resting_state.mp4':
        breathing_task_text = 'Resting state'
    breathing_task_text = breathing_task_text + " task: 6 min. \n Just follow the video for inhale and exhale, and relax. \n Press F1 to start."
    
    # Print debug information
    print(f"Participant: {participant_num}, Session: {session_num}")
    print(f"Current row: {current_row}")
    print(f"Breathing video: {expInfo['breathing_video']}")
    print(f"Interview video: {expInfo['interview_video']}")
    
    # Print all data stored in expInfo
    print(f"All data in expInfo: {expInfo}")
    
    # Initialize the wrong answer sound
    #wrong_sound = sound.Sound('sound/wrong.wav')
    # Replace the current wrong_sound initialization with:
    wrong_sound = sound.Sound('sound/wrong.wav')
    # Set experiment start values for variable component breath_duration
    breath_duration = 15
    breath_durationContainer = []
    # Set experiment start values for variable component stress_duration
    stress_duration = 2
    stress_durationContainer = []
    
    # --- Initialize components for Routine "_01_08_rest_state_trial" ---
    cross = visual.TextStim(win=win, name='cross',
        text='+',
        font='Open Sans',
        pos=(0, 0), height=0.05, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=-1.0);
    
    # --- Initialize components for Routine "_03_05_07_breathing_instr" ---
    text_3 = visual.TextStim(win=win, name='text_3',
        text=breathing_task_text,
        font='Open Sans',
        pos=(0, 0), height=0.03, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=0.0);
    key_resp_3 = keyboard.Keyboard()
    
    # --- Initialize components for Routine "_03_05_07_breathing_trial" ---
    
    # --- Initialize components for Routine "_03_05_07_breathing_instr" ---
    text_3 = visual.TextStim(win=win, name='text_3',
        text=breathing_task_text,
        font='Open Sans',
        pos=(0, 0), height=0.03, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=0.0);
    key_resp_3 = keyboard.Keyboard()
    
    # --- Initialize components for Routine "_03_05_07_breathing_trial" ---
    
    # --- Initialize components for Routine "_06_stroop_instr" ---
    stroop_instr = visual.TextStim(win=win, name='stroop_instr',
        text='Stroop task\nUse the arrow keys to identify the text color. Ignore what the word says.\n\nPress F1 to start.\n',
        font='Arial',
        pos=(0, 0), height=0.03, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=0.0);
    block_resp = keyboard.Keyboard()
    instrText_4 = visual.TextStim(win=win, name='instrText_4',
        text='red = ←, green = ↓, blue = → \nIdentify the COLOR of the text. Ignore what the word says',
        font='Arial',
        units='height', pos=(0, 0.4), height=0.03, wrapWidth=None, ori=0, 
        color='white', colorSpace='rgb', opacity=1, 
        languageStyle='LTR',
        depth=-2.0);
    
    # --- Initialize components for Routine "_06_stroop_trial" ---
    stim = visual.TextStim(win=win, name='stim',
        text='',
        font='Arial',
        units='height', pos=(0, 0), height=0.15, wrapWidth=None, ori=0, 
        color='white', colorSpace='rgb', opacity=1, 
        languageStyle='LTR',
        depth=-1.0);
    resp = keyboard.Keyboard()
    trial_counter = visual.TextBox2(
         win, text='', placeholder='Type here...', font='Arial',
         pos=(0, -.4),     letterHeight=0.05,
         size=(0.5, 0.1), borderWidth=2.0,
         color='black', colorSpace='rgb',
         opacity=0.8,
         bold=False, italic=False,
         lineSpacing=1.0, speechPoint=None,
         padding=0.0, alignment='center',
         anchor='center', overflow='visible',
         fillColor='white', borderColor='black',
         flipHoriz=False, flipVert=False, languageStyle='LTR',
         editable=False,
         name='trial_counter',
         depth=-3, autoLog=True,
    )
    instrText_2 = visual.TextStim(win=win, name='instrText_2',
        text='red = ←, green = ↓, blue = → \nIdentify the COLOR of the text. Ignore what the word says',
        font='Arial',
        units='height', pos=(0, 0.4), height=0.03, wrapWidth=None, ori=0, 
        color='white', colorSpace='rgb', opacity=1, 
        languageStyle='LTR',
        depth=-4.0);
    
    # --- Initialize components for Routine "_06_stroop_feedback" ---
    # Run 'Begin Experiment' code from code_2
    english_accuracy = []
    #maori_accuracy = []
    fbtxt = visual.TextBox2(
         win, text='', placeholder='Type here...', font='Arial',
         pos=(0, 0),     letterHeight=0.05,
         size=(0.5, 0.5), borderWidth=2.0,
         color='white', colorSpace='rgb',
         opacity=None,
         bold=False, italic=False,
         lineSpacing=1.0, speechPoint=None,
         padding=0.0, alignment='center',
         anchor='center', overflow='visible',
         fillColor=None, borderColor=None,
         flipHoriz=False, flipVert=False, languageStyle='LTR',
         editable=False,
         name='fbtxt',
         depth=-1, autoLog=True,
    )
    trial_counter_2 = visual.TextBox2(
         win, text='', placeholder='Type here...', font='Arial',
         pos=(0, -.4),     letterHeight=0.05,
         size=(0.5, 0.1), borderWidth=2.0,
         color='black', colorSpace='rgb',
         opacity=0.8,
         bold=False, italic=False,
         lineSpacing=1.0, speechPoint=None,
         padding=0.0, alignment='center',
         anchor='center', overflow='visible',
         fillColor='white', borderColor='black',
         flipHoriz=False, flipVert=False, languageStyle='LTR',
         editable=False,
         name='trial_counter_2',
         depth=-2, autoLog=True,
    )
    instrText_3 = visual.TextStim(win=win, name='instrText_3',
        text='red = ←, green = ↓, blue = → \nIdentify the COLOR of the text. Ignore what the word says',
        font='Arial',
        units='height', pos=(0, 0.4), height=0.03, wrapWidth=None, ori=0, 
        color='white', colorSpace='rgb', opacity=1, 
        languageStyle='LTR',
        depth=-3.0);
    
    # --- Initialize components for Routine "_03_05_07_breathing_instr" ---
    text_3 = visual.TextStim(win=win, name='text_3',
        text=breathing_task_text,
        font='Open Sans',
        pos=(0, 0), height=0.03, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=0.0);
    key_resp_3 = keyboard.Keyboard()
    
    # --- Initialize components for Routine "_03_05_07_breathing_trial" ---
    
    # --- Initialize components for Routine "_01_08_rest_state_trial" ---
    cross = visual.TextStim(win=win, name='cross',
        text='+',
        font='Open Sans',
        pos=(0, 0), height=0.05, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=-1.0);
    
    # --- Initialize components for Routine "End" ---
    EndMessage = visual.TextStim(win=win, name='EndMessage',
        text='Thank you!',
        font='Open Sans',
        pos=(0, 0), height=0.05, wrapWidth=None, ori=0.0, 
        color='white', colorSpace='rgb', opacity=None, 
        languageStyle='LTR',
        depth=-1.0);
    
    # create some handy timers
    if globalClock is None:
        globalClock = core.Clock()  # to track the time since experiment started
    if ioServer is not None:
        ioServer.syncClock(globalClock)
    logging.setDefaultClock(globalClock)
    routineTimer = core.Clock()  # to track time remaining of each (possibly non-slip) routine
    win.flip()  # flip window to reset last flip timer
    # store the exact time the global clock started
    expInfo['expStart'] = data.getDateStr(format='%Y-%m-%d %Hh%M.%S.%f %z', fractionalSecondDigits=6)
    
    # --- Prepare to start Routine "Load_LSL" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('Load_LSL.started', globalClock.getTime())
    # keep track of which components have finished
    Load_LSLComponents = []
    for thisComponent in Load_LSLComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "Load_LSL" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in Load_LSLComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "Load_LSL" ---
    for thisComponent in Load_LSLComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('Load_LSL.stopped', globalClock.getTime())
    # the Routine "Load_LSL" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "Experiment_Setup" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('Experiment_Setup.started', globalClock.getTime())
    # keep track of which components have finished
    Experiment_SetupComponents = []
    for thisComponent in Experiment_SetupComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "Experiment_Setup" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in Experiment_SetupComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "Experiment_Setup" ---
    for thisComponent in Experiment_SetupComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('Experiment_Setup.stopped', globalClock.getTime())
    thisExp.addData('breath_duration.expStartVal', 15)  # Save exp start value
    
    # the Routine "Experiment_Setup" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_01_08_rest_state_trial" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_01_08_rest_state_trial.started', globalClock.getTime())
    # Run 'Begin Routine' code from t_stim
    outlet.push_sample(x=[10])
    # keep track of which components have finished
    _01_08_rest_state_trialComponents = [cross]
    for thisComponent in _01_08_rest_state_trialComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_01_08_rest_state_trial" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *cross* updates
        
        # if cross is starting this frame...
        if cross.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            cross.frameNStart = frameN  # exact frame index
            cross.tStart = t  # local t and not account for scr refresh
            cross.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(cross, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'cross.started')
            # update status
            cross.status = STARTED
            cross.setAutoDraw(True)
        
        # if cross is active this frame...
        if cross.status == STARTED:
            # update params
            pass
        
        # if cross is stopping this frame...
        if cross.status == STARTED:
            # is it time to stop? (based on global clock, using actual start)
            if tThisFlipGlobal > cross.tStartRefresh + breath_duration-frameTolerance:
                # keep track of stop time/frame for later
                cross.tStop = t  # not accounting for scr refresh
                cross.frameNStop = frameN  # exact frame index
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'cross.stopped')
                # update status
                cross.status = FINISHED
                cross.setAutoDraw(False)
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _01_08_rest_state_trialComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_01_08_rest_state_trial" ---
    for thisComponent in _01_08_rest_state_trialComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_01_08_rest_state_trial.stopped', globalClock.getTime())
    # the Routine "_01_08_rest_state_trial" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_03_05_07_breathing_instr" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_03_05_07_breathing_instr.started', globalClock.getTime())
    key_resp_3.keys = []
    key_resp_3.rt = []
    _key_resp_3_allKeys = []
    # keep track of which components have finished
    _03_05_07_breathing_instrComponents = [text_3, key_resp_3]
    for thisComponent in _03_05_07_breathing_instrComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_03_05_07_breathing_instr" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *text_3* updates
        
        # if text_3 is starting this frame...
        if text_3.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            text_3.frameNStart = frameN  # exact frame index
            text_3.tStart = t  # local t and not account for scr refresh
            text_3.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(text_3, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'text_3.started')
            # update status
            text_3.status = STARTED
            text_3.setAutoDraw(True)
        
        # if text_3 is active this frame...
        if text_3.status == STARTED:
            # update params
            pass
        
        # *key_resp_3* updates
        waitOnFlip = False
        
        # if key_resp_3 is starting this frame...
        if key_resp_3.status == NOT_STARTED and tThisFlip >= 0-frameTolerance:
            # keep track of start time/frame for later
            key_resp_3.frameNStart = frameN  # exact frame index
            key_resp_3.tStart = t  # local t and not account for scr refresh
            key_resp_3.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(key_resp_3, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'key_resp_3.started')
            # update status
            key_resp_3.status = STARTED
            # keyboard checking is just starting
            waitOnFlip = True
            win.callOnFlip(key_resp_3.clock.reset)  # t=0 on next screen flip
            win.callOnFlip(key_resp_3.clearEvents, eventType='keyboard')  # clear events on next screen flip
        if key_resp_3.status == STARTED and not waitOnFlip:
            theseKeys = key_resp_3.getKeys(keyList=['f1'], ignoreKeys=["escape"], waitRelease=False)
            _key_resp_3_allKeys.extend(theseKeys)
            if len(_key_resp_3_allKeys):
                key_resp_3.keys = _key_resp_3_allKeys[-1].name  # just the last key pressed
                key_resp_3.rt = _key_resp_3_allKeys[-1].rt
                key_resp_3.duration = _key_resp_3_allKeys[-1].duration
                # a response ends the routine
                continueRoutine = False
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _03_05_07_breathing_instrComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_03_05_07_breathing_instr" ---
    for thisComponent in _03_05_07_breathing_instrComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_03_05_07_breathing_instr.stopped', globalClock.getTime())
    # check responses
    if key_resp_3.keys in ['', [], None]:  # No response was made
        key_resp_3.keys = None
    thisExp.addData('key_resp_3.keys',key_resp_3.keys)
    if key_resp_3.keys != None:  # we had a response
        thisExp.addData('key_resp_3.rt', key_resp_3.rt)
        thisExp.addData('key_resp_3.duration', key_resp_3.duration)
    thisExp.nextEntry()
    # Run 'End Routine' code from t_begin_3
    outlet.push_sample(x=[1])
    # the Routine "_03_05_07_breathing_instr" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_03_05_07_breathing_trial" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_03_05_07_breathing_trial.started', globalClock.getTime())
    # Run 'Begin Routine' code from t_stim_3
    outlet.push_sample(x=[int(31)])
    # Run 'Begin Routine' code from code_breathwork_vis
    # Paced Breathing Visualization for PsychoPy (height units)
    # To be pasted into a code component in a PsychoPy routine
    # Note: This code assumes 'win' (window object) is already defined in the PsychoPy environment
    # IDE warnings about undefined variables can be ignored as they will be defined in the PsychoPy environment
    
    from psychopy import visual, core, event
    import numpy as np
    
    # Check which breathing protocol to use (should be defined in experiment)
    # breath_protocol should be either "paced_breathing" or "fast_with_breath_hold"
    breath_protocol = "fast_with_breath_hold"  # Default value
    
    # Breathing settings based on protocol
    if breath_protocol == "paced_breathing":
        # Mode 1: Original paced breathing
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
        # Check if breath_duration is defined in the experiment
        try:
            # Try to access breath_duration
            total_duration = breath_duration  # Use variable from experiment
        except NameError:
            # If not defined, set a default value
            total_duration = 360  # Default to 6 minutes
            print("Warning: breath_duration not defined, using default: 300 seconds")
    else:  # fast_with_breath_hold
        # Mode 2: Fast breathing with holds
        # Each round: 30 rapid breaths + exhale hold + inhale hold
        fast_inhale_time = 1.5
        fast_exhale_time = 1.5
        fast_cycle_time = fast_inhale_time + fast_exhale_time
        num_fast_breaths = 30
        fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
        exhale_hold_duration = 15
        inhale_hold_duration = 15
        round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
        num_rounds = 3
    
        # Use breath_duration as hard cutoff, same as paced_breathing mode
        try:
            # Try to access breath_duration
            total_duration = breath_duration  # Use variable from experiment
        except NameError:
            # If not defined, set a default value
            total_duration = 360  # Default to 6 minutes
            print("Warning: breath_duration not defined, using default: 360 seconds")
    
        # Define these variables for the fast_with_breath_hold mode as well
        # to prevent "referenced before assignment" errors
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
    # Get window dimensions in height units
    # In height units, the height is always 1.0 (from -0.5 to 0.5)
    # The width depends on the aspect ratio
    win_height = 1.0
    win_width = win.size[0] / win.size[1]  # This gives us the aspect ratio
    
    # Normalize horizontal distance to window width
    # Leave some margin on both sides (10% of width)
    margin = 0.1 * win_width
    usable_width = win_width - 2 * margin
    x_start = -win_width/2 + margin
    x_end = win_width/2 - margin
    
    # Vertical parameters (in height units)
    y_min = -0.25  # Bottom position for exhale
    y_max = 0.25   # Top position for inhale
    y_range = y_max - y_min
    
    # Create the breathing path line segments
    # Calculate segment widths based on their duration
    if breath_protocol == "paced_breathing":
        # Original path vertices for paced breathing
        inhale_width = (inhale_time / total_cycle_time) * usable_width
        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
        exhale_width = (exhale_time / total_cycle_time) * usable_width
        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width
    
        vertices = [
            [x_start, y_min],                                  # Start point
            [x_start + inhale_width, y_max],                   # End of inhale
            [x_start + inhale_width + inhale_hold_width, y_max],  # End of inhale hold
            [x_start + inhale_width + inhale_hold_width + exhale_width, y_min],  # End of exhale
            [x_end, y_min]                                     # End of exhale hold
        ]
    else:
        # For fast_with_breath_hold, we need two different path visualizations:
        # 1. Fast breathing path (first 90 seconds of each round)
        # 2. Hold phases path (last 30 seconds of each round)
    
        # Calculate the current phase based on time
        current_time = 0  # This will be updated in the animation loop
        round_num = int(current_time / round_duration)
        round_time = current_time - (round_num * round_duration)
    
        # Fast breathing path parameters
        fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
        if round_time < fast_breathing_duration:
            # Fast breathing path - just up and down, no holds
            vertices = [
                [x_start, y_min],                      # Start point
                [x_start + fast_inhale_width, y_max],  # End of inhale
                [x_end, y_min]                         # End of exhale
            ]
        else:
            # Hold phases path - spanning the entire usable width
            # Define the segments of the hold cycle
            hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
            exhale_hold_segment = 14.5  # seconds
            inhale_transition = 0.5     # seconds
            inhale_hold_segment = 14.5  # seconds
            exhale_transition = 0.5     # seconds
    
            # Calculate segment widths based on the entire usable width
            exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
            inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
            exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
            vertices = [
                [x_start, y_min],                                                # Start of exhale hold
                [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                [x_end, y_min]                                                   # End of exhale transition
            ]
    
    # Create the path line
    path_line = visual.ShapeStim(
        win=win,
        vertices=vertices,
        closeShape=False,
        lineWidth=3,
        lineColor='white',
        opacity=0.7
    )
    
    # Create the ball that will travel along the path
    ball = visual.Circle(
        win=win,
        radius=0.015,  # Adjusted for height units
        fillColor='lightblue',
        lineColor='white',
        lineWidth=2
    )
    
    # Create background shading rectangle
    background = visual.Rect(
        win=win,
        width=win_width,
        height=y_range,
        fillColor='skyblue',
        opacity=0.2,
        pos=[0, y_min + y_range/2]  # Start at the bottom
    )
    
    # Function to calculate ball position at a given time
    def get_ball_position(current_time):
        if breath_protocol == "paced_breathing":
            # Define breathing parameters for paced breathing
            inhale_time = 4.5
            inhale_hold_time = 0.5
            exhale_time = 4.5
            exhale_hold_time = 0.5
            total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
            # Original ball position calculation
            cycle_time = current_time % total_cycle_time
    
            # Calculate segment widths here to ensure they're defined
            inhale_width = (inhale_time / total_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
            exhale_width = (exhale_time / total_cycle_time) * usable_width
            exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width
    
            # Determine which segment we're in
            if cycle_time < inhale_time:
                # Inhale segment - moving up
                progress = cycle_time / inhale_time
                x = x_start + progress * inhale_width
                y = y_min + progress * y_range
                phase = "Inhale"
    
            elif cycle_time < inhale_time + inhale_hold_time:
                # Inhale hold segment - staying at the top
                progress = (cycle_time - inhale_time) / inhale_hold_time
                x = x_start + inhale_width + progress * inhale_hold_width
                y = y_max
                phase = "Hold"
    
            elif cycle_time < inhale_time + inhale_hold_time + exhale_time:
                # Exhale segment - moving down
                progress = (cycle_time - inhale_time - inhale_hold_time) / exhale_time
                x = x_start + inhale_width + inhale_hold_width + progress * exhale_width
                y = y_max - progress * y_range
                phase = "Exhale"
    
            else:
                # Exhale hold segment - staying at the bottom
                progress = (cycle_time - inhale_time - inhale_hold_time - exhale_time) / exhale_hold_time
                x = x_start + inhale_width + inhale_hold_width + exhale_width + progress * exhale_hold_width
                y = y_min
                phase = "Hold"
    
        else:  # fast_with_breath_hold
            # Define breathing parameters for fast breathing with holds
            fast_inhale_time = 1.5
            fast_exhale_time = 1.5
            fast_cycle_time = fast_inhale_time + fast_exhale_time
            num_fast_breaths = 30
            fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
            exhale_hold_duration = 15
            inhale_hold_duration = 15
            round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
            num_rounds = 3
    
            # Calculate which round we're in
            round_num = int(current_time / round_duration)
            if round_num >= num_rounds:
                round_num = num_rounds - 1
    
            # Calculate time within the current round
            round_time = current_time - (round_num * round_duration)
    
            # Determine which phase we're in
            if round_time < fast_breathing_duration:
                # Fast breathing phase
                cycle_num = int(round_time / fast_cycle_time)
                cycle_time = round_time % fast_cycle_time
    
                # Fast breathing path parameters - ensure consistent with path creation
                fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
                if cycle_time < fast_inhale_time:
                    # Inhale segment - moving up
                    progress = cycle_time / fast_inhale_time
                    x = x_start + progress * fast_inhale_width
                    y = y_min + progress * y_range
                    phase = "Inhale"
                else:
                    # Exhale segment - moving down
                    progress = (cycle_time - fast_inhale_time) / fast_exhale_time
                    # Use the remaining width to ensure ball reaches end point
                    remaining_width = usable_width - fast_inhale_width
                    x = x_start + fast_inhale_width + progress * remaining_width
                    y = y_max - progress * y_range
                    phase = "Exhale"
    
                # Add breath counter to phase
                phase += f" ({cycle_num+1}/{num_fast_breaths})"
    
            else:
                # Hold phases (combined exhale hold and inhale hold)
                hold_time = round_time - fast_breathing_duration
    
                # Calculate position in the hold cycle
                hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
    
                # Define the segments of the hold cycle - ensure consistent with path creation
                exhale_hold_segment = 14.5  # seconds
                inhale_transition = 0.5     # seconds
                inhale_hold_segment = 14.5  # seconds
                exhale_transition = 0.5     # seconds
    
                # Calculate segment widths using the entire usable width - ensure consistent with path creation
                exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
                # Determine which segment of the hold cycle we're in
                if hold_time < exhale_hold_segment:
                    # Exhale hold (bottom)
                    progress = hold_time / exhale_hold_segment
                    x = x_start + progress * exhale_hold_width
                    y = y_min
                    phase = f"Exhaled Hold ({int(exhale_hold_segment - hold_time)}s)"
    
                elif hold_time < exhale_hold_segment + inhale_transition:
                    # Quick inhale transition (moving up)
                    progress = (hold_time - exhale_hold_segment) / inhale_transition
                    x = x_start + exhale_hold_width + progress * inhale_trans_width
                    y = y_min + progress * y_range
                    phase = "Inhale"
    
                elif hold_time < exhale_hold_segment + inhale_transition + inhale_hold_segment:
                    # Inhale hold (top)
                    progress = (hold_time - exhale_hold_segment - inhale_transition) / inhale_hold_segment
                    x = x_start + exhale_hold_width + inhale_trans_width + progress * inhale_hold_width
                    y = y_max
                    phase = f"Inhaled Hold ({int(inhale_hold_segment - (progress * inhale_hold_segment))}s)"
    
                else:
                    # Quick exhale transition (moving down)
                    progress = (hold_time - exhale_hold_segment - inhale_transition - inhale_hold_segment) / exhale_transition
                    x = x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width + progress * exhale_trans_width
                    y = y_max - progress * y_range
                    phase = "Exhale"
    
            # Add round information
            phase = f"Round {round_num+1}/{num_rounds}: " + phase
    
        return x, y, phase
    
    # Create text display for the breathing phase
    phase_text = visual.TextStim(
        win=win,
        text="Inhale",
        pos=[0, -0.35],
        color='white',
        height=0.05  # Adjusted for height units
    )
    
    # Create a timer display
    timer_text = visual.TextStim(
        win=win,
        text="",
        pos=[0, 0.4],
        color='white',
        height=0.03  # Adjusted for height units
    )
    
    # Create breath counter rectangles for fast_with_breath_hold mode
    breath_rects = []
    round_rects = []
    if breath_protocol == "fast_with_breath_hold":
        # Calculate rectangle dimensions and positions
        rect_height = 0.05  # Height of each rectangle
        rect_width = 0.02   # Width of each rectangle
        rect_spacing = 0.005  # Space between rectangles
    
        # Position in right 2/3 of screen
        screen_left = -win_width/2
        screen_right = win_width/2
        left_third_end = screen_left + win_width/3
    
        # Calculate total width needed for all breath rectangles
        total_rect_width = num_fast_breaths * (rect_width + rect_spacing) - rect_spacing
    
        # Center the rectangles in the right 2/3 of the screen
        rect_area_start = left_third_end
        rect_area_end = screen_right
        rect_area_width = rect_area_end - rect_area_start
    
        # Start position for the first rectangle - leave space for the "Breaths:" text
        rect_start_x = left_third_end + 0.25  # Adjusted to leave space for text
    
        # Create all breath rectangles - initially gray with white outline
        for i in range(num_fast_breaths):
            rect = visual.Rect(
                win=win,
                width=rect_width,
                height=rect_height,
                fillColor='gray',  # Start with gray fill
                lineColor='white',
                lineWidth=1,
                opacity=0.3,  # Make the gray semi-transparent
                pos=[rect_start_x + i * (rect_width + rect_spacing), -0.4]
            )
            breath_rects.append(rect)
    
        # Create round counter rectangles
        # Calculate dimensions for round rectangles (slightly larger)
        round_rect_height = 0.06
        round_rect_width = 0.03
        round_rect_spacing = 0.01
    
        # Position round rectangles in the left 1/4 of the screen
        left_quarter_end = screen_left + win_width/4
        round_rect_start_x = left_quarter_end - 0.15  # Positioned to the left of the round text
    
        # Create round rectangles - initially gray with white outline
        for i in range(num_rounds):
            rect = visual.Rect(
                win=win,
                width=round_rect_width,
                height=round_rect_height,
                fillColor='gray',  # Start with gray fill
                lineColor='white',
                lineWidth=1,
                opacity=0.3,  # Make the gray semi-transparent
                pos=[round_rect_start_x + i * (round_rect_width + round_rect_spacing), -0.4]
            )
            round_rects.append(rect)
    
    # Create round counter text for fast_with_breath_hold mode
    round_text = None
    breaths_remaining_text = None
    if breath_protocol == "fast_with_breath_hold":
        # Round counter text - positioned at the right side of the left 1/4
        left_quarter_end = screen_left + win_width/4
        round_text = visual.TextStim(
            win=win,
            text="Round:",
            pos=[left_quarter_end - 0.35, -0.4],  # Moved further left so text ends ~2 chars before previous start
            color='white',
            height=0.05,  # Adjusted for height units
            alignHoriz='left'  # Align text to the left for consistent positioning
        )
    
        # Breaths remaining text - positioned before the rectangles
        breaths_remaining_text = visual.TextStim(
            win=win,
            text="Breaths:",
            pos=[left_third_end + 0.05, -0.4],  # Left side of right 2/3, moved left
            color='white',
            height=0.05,  # Adjusted for height units
            alignHoriz='left'
        )
    
    # Main animation loop
    timer = core.Clock()
    continue_routine = True
    
    try:
        while continue_routine:
            # Get current time
            t = timer.getTime()
    
            # Check if we've reached the total duration
            if t >= total_duration:
                continue_routine = False
                break
    
            # For fast_with_breath_hold mode, update the path visualization based on the current phase
            if breath_protocol == "fast_with_breath_hold":
                # Define these parameters inside the loop to ensure they're available
                fast_inhale_time = 1.5
                fast_exhale_time = 1.5
                fast_cycle_time = fast_inhale_time + fast_exhale_time
                num_fast_breaths = 30
                fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
                exhale_hold_duration = 15
                inhale_hold_duration = 15
                round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
                num_rounds = 3
    
                round_num = int(t / round_duration)
                if round_num >= num_rounds:
                    round_num = num_rounds - 1
    
                round_time = t - (round_num * round_duration)
    
                # Check if we need to switch path visualization
                is_hold_phase = round_time >= fast_breathing_duration
    
                # Check if path_line exists and has vertices before accessing them
                if hasattr(path_line, 'vertices') and len(path_line.vertices) > 0:
                    # Fast breathing path parameters
                    fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
                    # Calculate hold phase parameters - using the entire usable width
                    hold_cycle_time = exhale_hold_duration + inhale_hold_duration
                    exhale_hold_segment = 14.5
                    inhale_transition = 0.5
                    inhale_hold_segment = 14.5
                    exhale_transition = 0.5
    
                    # Calculate segment widths using the entire usable width
                    exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                    inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                    inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                    exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
                    # Check the current phase and update path if needed
                    if is_hold_phase and len(path_line.vertices) == 3:
                        # Switch to hold phase path - spanning the entire usable width
                        path_line.vertices = [
                            [x_start, y_min],                                                # Start of exhale hold
                            [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                            [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                            [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                            [x_end, y_min]                                                   # End of exhale transition
                        ]
    
                    elif not is_hold_phase and len(path_line.vertices) == 5:
                        # Switch back to fast breathing path
                        path_line.vertices = [
                            [x_start, y_min],
                            [x_start + fast_inhale_width, y_max],
                            [x_end, y_min]
                        ]
    
            # Calculate current ball position
            x, y, phase = get_ball_position(t)
    
            # Update ball position
            ball.pos = [x, y]
    
            # Update background height and position
            background.height = y - y_min
            background.pos = [0, y_min + (y - y_min)/2]
    
            # Update phase text
            phase_text.text = phase
    
            # Update timer text - show remaining time
            time_left = int(total_duration - t)
            timer_text.text = f"Time remaining: {time_left}s"
    
            # Draw stimuli
            background.draw()
            path_line.draw()
            ball.draw()
            timer_text.draw()  # Keep timer text
    
            # Draw breath counter rectangles and round counter for fast_with_breath_hold mode
            if breath_protocol == "fast_with_breath_hold":
                # Draw round text (static "Round:" label)
                if round_text:
                    round_text.draw()
    
                # Draw breaths remaining text
                if breaths_remaining_text:
                    breaths_remaining_text.draw()
    
                # Update and draw breath rectangles
                if breath_rects:
                    # Calculate how many breaths have been completed in the current round
                    if round_time < fast_breathing_duration:
                        completed_breaths = int(round_time / fast_cycle_time)
                    else:
                        completed_breaths = num_fast_breaths  # All breaths completed
    
                    # Draw all breath rectangles - advance by 1 so current breath is already filled
                    for i, rect in enumerate(breath_rects):
                        # For the current breath cycle and completed ones
                        if i <= completed_breaths:  # Changed from < to <= to include current breath
                            # Fill current and completed breath rectangles with lightblue
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future breath rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
    
                # Update and draw round rectangles
                if round_rects:
                    # Draw all round rectangles
                    for i, rect in enumerate(round_rects):
                        if i <= round_num:  # Fill current and previous rounds
                            # Fill completed round rectangles with same blue as breath counter
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future round rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
            else:
                # For paced_breathing mode, still show the phase text
                phase_text.draw()
    
            # Check for quit (the Esc key)
            keys = event.getKeys(keyList=["escape"])
            if 'escape' in keys:
                continue_routine = False
                core.quit()  # This will exit the experiment
    
            # Refresh the screen
            win.flip()
    except Exception as e:
        print(f"Error in animation loop: {e}")
        # Handle the error appropriately for your experiment
    finally:
        # Clean up
        import gc
        gc.collect()  # Force garbage collection
    
    # Just clear the screen and wait
    try:
        win.flip()
        core.wait(2.0)
    except Exception as e:
        print(f"Error in final screen clear: {e}")
    
    # keep track of which components have finished
    _03_05_07_breathing_trialComponents = []
    for thisComponent in _03_05_07_breathing_trialComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_03_05_07_breathing_trial" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _03_05_07_breathing_trialComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_03_05_07_breathing_trial" ---
    for thisComponent in _03_05_07_breathing_trialComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_03_05_07_breathing_trial.stopped', globalClock.getTime())
    # the Routine "_03_05_07_breathing_trial" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_03_05_07_breathing_instr" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_03_05_07_breathing_instr.started', globalClock.getTime())
    key_resp_3.keys = []
    key_resp_3.rt = []
    _key_resp_3_allKeys = []
    # keep track of which components have finished
    _03_05_07_breathing_instrComponents = [text_3, key_resp_3]
    for thisComponent in _03_05_07_breathing_instrComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_03_05_07_breathing_instr" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *text_3* updates
        
        # if text_3 is starting this frame...
        if text_3.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            text_3.frameNStart = frameN  # exact frame index
            text_3.tStart = t  # local t and not account for scr refresh
            text_3.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(text_3, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'text_3.started')
            # update status
            text_3.status = STARTED
            text_3.setAutoDraw(True)
        
        # if text_3 is active this frame...
        if text_3.status == STARTED:
            # update params
            pass
        
        # *key_resp_3* updates
        waitOnFlip = False
        
        # if key_resp_3 is starting this frame...
        if key_resp_3.status == NOT_STARTED and tThisFlip >= 0-frameTolerance:
            # keep track of start time/frame for later
            key_resp_3.frameNStart = frameN  # exact frame index
            key_resp_3.tStart = t  # local t and not account for scr refresh
            key_resp_3.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(key_resp_3, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'key_resp_3.started')
            # update status
            key_resp_3.status = STARTED
            # keyboard checking is just starting
            waitOnFlip = True
            win.callOnFlip(key_resp_3.clock.reset)  # t=0 on next screen flip
            win.callOnFlip(key_resp_3.clearEvents, eventType='keyboard')  # clear events on next screen flip
        if key_resp_3.status == STARTED and not waitOnFlip:
            theseKeys = key_resp_3.getKeys(keyList=['f1'], ignoreKeys=["escape"], waitRelease=False)
            _key_resp_3_allKeys.extend(theseKeys)
            if len(_key_resp_3_allKeys):
                key_resp_3.keys = _key_resp_3_allKeys[-1].name  # just the last key pressed
                key_resp_3.rt = _key_resp_3_allKeys[-1].rt
                key_resp_3.duration = _key_resp_3_allKeys[-1].duration
                # a response ends the routine
                continueRoutine = False
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _03_05_07_breathing_instrComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_03_05_07_breathing_instr" ---
    for thisComponent in _03_05_07_breathing_instrComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_03_05_07_breathing_instr.stopped', globalClock.getTime())
    # check responses
    if key_resp_3.keys in ['', [], None]:  # No response was made
        key_resp_3.keys = None
    thisExp.addData('key_resp_3.keys',key_resp_3.keys)
    if key_resp_3.keys != None:  # we had a response
        thisExp.addData('key_resp_3.rt', key_resp_3.rt)
        thisExp.addData('key_resp_3.duration', key_resp_3.duration)
    thisExp.nextEntry()
    # Run 'End Routine' code from t_begin_3
    outlet.push_sample(x=[1])
    # the Routine "_03_05_07_breathing_instr" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_03_05_07_breathing_trial" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_03_05_07_breathing_trial.started', globalClock.getTime())
    # Run 'Begin Routine' code from t_stim_3
    outlet.push_sample(x=[int(31)])
    # Run 'Begin Routine' code from code_breathwork_vis
    # Paced Breathing Visualization for PsychoPy (height units)
    # To be pasted into a code component in a PsychoPy routine
    # Note: This code assumes 'win' (window object) is already defined in the PsychoPy environment
    # IDE warnings about undefined variables can be ignored as they will be defined in the PsychoPy environment
    
    from psychopy import visual, core, event
    import numpy as np
    
    # Check which breathing protocol to use (should be defined in experiment)
    # breath_protocol should be either "paced_breathing" or "fast_with_breath_hold"
    breath_protocol = "fast_with_breath_hold"  # Default value
    
    # Breathing settings based on protocol
    if breath_protocol == "paced_breathing":
        # Mode 1: Original paced breathing
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
        # Check if breath_duration is defined in the experiment
        try:
            # Try to access breath_duration
            total_duration = breath_duration  # Use variable from experiment
        except NameError:
            # If not defined, set a default value
            total_duration = 360  # Default to 6 minutes
            print("Warning: breath_duration not defined, using default: 300 seconds")
    else:  # fast_with_breath_hold
        # Mode 2: Fast breathing with holds
        # Each round: 30 rapid breaths + exhale hold + inhale hold
        fast_inhale_time = 1.5
        fast_exhale_time = 1.5
        fast_cycle_time = fast_inhale_time + fast_exhale_time
        num_fast_breaths = 30
        fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
        exhale_hold_duration = 15
        inhale_hold_duration = 15
        round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
        num_rounds = 3
    
        # Use breath_duration as hard cutoff, same as paced_breathing mode
        try:
            # Try to access breath_duration
            total_duration = breath_duration  # Use variable from experiment
        except NameError:
            # If not defined, set a default value
            total_duration = 360  # Default to 6 minutes
            print("Warning: breath_duration not defined, using default: 360 seconds")
    
        # Define these variables for the fast_with_breath_hold mode as well
        # to prevent "referenced before assignment" errors
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
    # Get window dimensions in height units
    # In height units, the height is always 1.0 (from -0.5 to 0.5)
    # The width depends on the aspect ratio
    win_height = 1.0
    win_width = win.size[0] / win.size[1]  # This gives us the aspect ratio
    
    # Normalize horizontal distance to window width
    # Leave some margin on both sides (10% of width)
    margin = 0.1 * win_width
    usable_width = win_width - 2 * margin
    x_start = -win_width/2 + margin
    x_end = win_width/2 - margin
    
    # Vertical parameters (in height units)
    y_min = -0.25  # Bottom position for exhale
    y_max = 0.25   # Top position for inhale
    y_range = y_max - y_min
    
    # Create the breathing path line segments
    # Calculate segment widths based on their duration
    if breath_protocol == "paced_breathing":
        # Original path vertices for paced breathing
        inhale_width = (inhale_time / total_cycle_time) * usable_width
        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
        exhale_width = (exhale_time / total_cycle_time) * usable_width
        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width
    
        vertices = [
            [x_start, y_min],                                  # Start point
            [x_start + inhale_width, y_max],                   # End of inhale
            [x_start + inhale_width + inhale_hold_width, y_max],  # End of inhale hold
            [x_start + inhale_width + inhale_hold_width + exhale_width, y_min],  # End of exhale
            [x_end, y_min]                                     # End of exhale hold
        ]
    else:
        # For fast_with_breath_hold, we need two different path visualizations:
        # 1. Fast breathing path (first 90 seconds of each round)
        # 2. Hold phases path (last 30 seconds of each round)
    
        # Calculate the current phase based on time
        current_time = 0  # This will be updated in the animation loop
        round_num = int(current_time / round_duration)
        round_time = current_time - (round_num * round_duration)
    
        # Fast breathing path parameters
        fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
        if round_time < fast_breathing_duration:
            # Fast breathing path - just up and down, no holds
            vertices = [
                [x_start, y_min],                      # Start point
                [x_start + fast_inhale_width, y_max],  # End of inhale
                [x_end, y_min]                         # End of exhale
            ]
        else:
            # Hold phases path - spanning the entire usable width
            # Define the segments of the hold cycle
            hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
            exhale_hold_segment = 14.5  # seconds
            inhale_transition = 0.5     # seconds
            inhale_hold_segment = 14.5  # seconds
            exhale_transition = 0.5     # seconds
    
            # Calculate segment widths based on the entire usable width
            exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
            inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
            exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
            vertices = [
                [x_start, y_min],                                                # Start of exhale hold
                [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                [x_end, y_min]                                                   # End of exhale transition
            ]
    
    # Create the path line
    path_line = visual.ShapeStim(
        win=win,
        vertices=vertices,
        closeShape=False,
        lineWidth=3,
        lineColor='white',
        opacity=0.7
    )
    
    # Create the ball that will travel along the path
    ball = visual.Circle(
        win=win,
        radius=0.015,  # Adjusted for height units
        fillColor='lightblue',
        lineColor='white',
        lineWidth=2
    )
    
    # Create background shading rectangle
    background = visual.Rect(
        win=win,
        width=win_width,
        height=y_range,
        fillColor='skyblue',
        opacity=0.2,
        pos=[0, y_min + y_range/2]  # Start at the bottom
    )
    
    # Function to calculate ball position at a given time
    def get_ball_position(current_time):
        if breath_protocol == "paced_breathing":
            # Define breathing parameters for paced breathing
            inhale_time = 4.5
            inhale_hold_time = 0.5
            exhale_time = 4.5
            exhale_hold_time = 0.5
            total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
            # Original ball position calculation
            cycle_time = current_time % total_cycle_time
    
            # Calculate segment widths here to ensure they're defined
            inhale_width = (inhale_time / total_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
            exhale_width = (exhale_time / total_cycle_time) * usable_width
            exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width
    
            # Determine which segment we're in
            if cycle_time < inhale_time:
                # Inhale segment - moving up
                progress = cycle_time / inhale_time
                x = x_start + progress * inhale_width
                y = y_min + progress * y_range
                phase = "Inhale"
    
            elif cycle_time < inhale_time + inhale_hold_time:
                # Inhale hold segment - staying at the top
                progress = (cycle_time - inhale_time) / inhale_hold_time
                x = x_start + inhale_width + progress * inhale_hold_width
                y = y_max
                phase = "Hold"
    
            elif cycle_time < inhale_time + inhale_hold_time + exhale_time:
                # Exhale segment - moving down
                progress = (cycle_time - inhale_time - inhale_hold_time) / exhale_time
                x = x_start + inhale_width + inhale_hold_width + progress * exhale_width
                y = y_max - progress * y_range
                phase = "Exhale"
    
            else:
                # Exhale hold segment - staying at the bottom
                progress = (cycle_time - inhale_time - inhale_hold_time - exhale_time) / exhale_hold_time
                x = x_start + inhale_width + inhale_hold_width + exhale_width + progress * exhale_hold_width
                y = y_min
                phase = "Hold"
    
        else:  # fast_with_breath_hold
            # Define breathing parameters for fast breathing with holds
            fast_inhale_time = 1.5
            fast_exhale_time = 1.5
            fast_cycle_time = fast_inhale_time + fast_exhale_time
            num_fast_breaths = 30
            fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
            exhale_hold_duration = 15
            inhale_hold_duration = 15
            round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
            num_rounds = 3
    
            # Calculate which round we're in
            round_num = int(current_time / round_duration)
            if round_num >= num_rounds:
                round_num = num_rounds - 1
    
            # Calculate time within the current round
            round_time = current_time - (round_num * round_duration)
    
            # Determine which phase we're in
            if round_time < fast_breathing_duration:
                # Fast breathing phase
                cycle_num = int(round_time / fast_cycle_time)
                cycle_time = round_time % fast_cycle_time
    
                # Fast breathing path parameters - ensure consistent with path creation
                fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
                if cycle_time < fast_inhale_time:
                    # Inhale segment - moving up
                    progress = cycle_time / fast_inhale_time
                    x = x_start + progress * fast_inhale_width
                    y = y_min + progress * y_range
                    phase = "Inhale"
                else:
                    # Exhale segment - moving down
                    progress = (cycle_time - fast_inhale_time) / fast_exhale_time
                    # Use the remaining width to ensure ball reaches end point
                    remaining_width = usable_width - fast_inhale_width
                    x = x_start + fast_inhale_width + progress * remaining_width
                    y = y_max - progress * y_range
                    phase = "Exhale"
    
                # Add breath counter to phase
                phase += f" ({cycle_num+1}/{num_fast_breaths})"
    
            else:
                # Hold phases (combined exhale hold and inhale hold)
                hold_time = round_time - fast_breathing_duration
    
                # Calculate position in the hold cycle
                hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
    
                # Define the segments of the hold cycle - ensure consistent with path creation
                exhale_hold_segment = 14.5  # seconds
                inhale_transition = 0.5     # seconds
                inhale_hold_segment = 14.5  # seconds
                exhale_transition = 0.5     # seconds
    
                # Calculate segment widths using the entire usable width - ensure consistent with path creation
                exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
                # Determine which segment of the hold cycle we're in
                if hold_time < exhale_hold_segment:
                    # Exhale hold (bottom)
                    progress = hold_time / exhale_hold_segment
                    x = x_start + progress * exhale_hold_width
                    y = y_min
                    phase = f"Exhaled Hold ({int(exhale_hold_segment - hold_time)}s)"
    
                elif hold_time < exhale_hold_segment + inhale_transition:
                    # Quick inhale transition (moving up)
                    progress = (hold_time - exhale_hold_segment) / inhale_transition
                    x = x_start + exhale_hold_width + progress * inhale_trans_width
                    y = y_min + progress * y_range
                    phase = "Inhale"
    
                elif hold_time < exhale_hold_segment + inhale_transition + inhale_hold_segment:
                    # Inhale hold (top)
                    progress = (hold_time - exhale_hold_segment - inhale_transition) / inhale_hold_segment
                    x = x_start + exhale_hold_width + inhale_trans_width + progress * inhale_hold_width
                    y = y_max
                    phase = f"Inhaled Hold ({int(inhale_hold_segment - (progress * inhale_hold_segment))}s)"
    
                else:
                    # Quick exhale transition (moving down)
                    progress = (hold_time - exhale_hold_segment - inhale_transition - inhale_hold_segment) / exhale_transition
                    x = x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width + progress * exhale_trans_width
                    y = y_max - progress * y_range
                    phase = "Exhale"
    
            # Add round information
            phase = f"Round {round_num+1}/{num_rounds}: " + phase
    
        return x, y, phase
    
    # Create text display for the breathing phase
    phase_text = visual.TextStim(
        win=win,
        text="Inhale",
        pos=[0, -0.35],
        color='white',
        height=0.05  # Adjusted for height units
    )
    
    # Create a timer display
    timer_text = visual.TextStim(
        win=win,
        text="",
        pos=[0, 0.4],
        color='white',
        height=0.03  # Adjusted for height units
    )
    
    # Create breath counter rectangles for fast_with_breath_hold mode
    breath_rects = []
    round_rects = []
    if breath_protocol == "fast_with_breath_hold":
        # Calculate rectangle dimensions and positions
        rect_height = 0.05  # Height of each rectangle
        rect_width = 0.02   # Width of each rectangle
        rect_spacing = 0.005  # Space between rectangles
    
        # Position in right 2/3 of screen
        screen_left = -win_width/2
        screen_right = win_width/2
        left_third_end = screen_left + win_width/3
    
        # Calculate total width needed for all breath rectangles
        total_rect_width = num_fast_breaths * (rect_width + rect_spacing) - rect_spacing
    
        # Center the rectangles in the right 2/3 of the screen
        rect_area_start = left_third_end
        rect_area_end = screen_right
        rect_area_width = rect_area_end - rect_area_start
    
        # Start position for the first rectangle - leave space for the "Breaths:" text
        rect_start_x = left_third_end + 0.25  # Adjusted to leave space for text
    
        # Create all breath rectangles - initially gray with white outline
        for i in range(num_fast_breaths):
            rect = visual.Rect(
                win=win,
                width=rect_width,
                height=rect_height,
                fillColor='gray',  # Start with gray fill
                lineColor='white',
                lineWidth=1,
                opacity=0.3,  # Make the gray semi-transparent
                pos=[rect_start_x + i * (rect_width + rect_spacing), -0.4]
            )
            breath_rects.append(rect)
    
        # Create round counter rectangles
        # Calculate dimensions for round rectangles (slightly larger)
        round_rect_height = 0.06
        round_rect_width = 0.03
        round_rect_spacing = 0.01
    
        # Position round rectangles in the left 1/4 of the screen
        left_quarter_end = screen_left + win_width/4
        round_rect_start_x = left_quarter_end - 0.15  # Positioned to the left of the round text
    
        # Create round rectangles - initially gray with white outline
        for i in range(num_rounds):
            rect = visual.Rect(
                win=win,
                width=round_rect_width,
                height=round_rect_height,
                fillColor='gray',  # Start with gray fill
                lineColor='white',
                lineWidth=1,
                opacity=0.3,  # Make the gray semi-transparent
                pos=[round_rect_start_x + i * (round_rect_width + round_rect_spacing), -0.4]
            )
            round_rects.append(rect)
    
    # Create round counter text for fast_with_breath_hold mode
    round_text = None
    breaths_remaining_text = None
    if breath_protocol == "fast_with_breath_hold":
        # Round counter text - positioned at the right side of the left 1/4
        left_quarter_end = screen_left + win_width/4
        round_text = visual.TextStim(
            win=win,
            text="Round:",
            pos=[left_quarter_end - 0.35, -0.4],  # Moved further left so text ends ~2 chars before previous start
            color='white',
            height=0.05,  # Adjusted for height units
            alignHoriz='left'  # Align text to the left for consistent positioning
        )
    
        # Breaths remaining text - positioned before the rectangles
        breaths_remaining_text = visual.TextStim(
            win=win,
            text="Breaths:",
            pos=[left_third_end + 0.05, -0.4],  # Left side of right 2/3, moved left
            color='white',
            height=0.05,  # Adjusted for height units
            alignHoriz='left'
        )
    
    # Main animation loop
    timer = core.Clock()
    continue_routine = True
    
    try:
        while continue_routine:
            # Get current time
            t = timer.getTime()
    
            # Check if we've reached the total duration
            if t >= total_duration:
                continue_routine = False
                break
    
            # For fast_with_breath_hold mode, update the path visualization based on the current phase
            if breath_protocol == "fast_with_breath_hold":
                # Define these parameters inside the loop to ensure they're available
                fast_inhale_time = 1.5
                fast_exhale_time = 1.5
                fast_cycle_time = fast_inhale_time + fast_exhale_time
                num_fast_breaths = 30
                fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
                exhale_hold_duration = 15
                inhale_hold_duration = 15
                round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
                num_rounds = 3
    
                round_num = int(t / round_duration)
                if round_num >= num_rounds:
                    round_num = num_rounds - 1
    
                round_time = t - (round_num * round_duration)
    
                # Check if we need to switch path visualization
                is_hold_phase = round_time >= fast_breathing_duration
    
                # Check if path_line exists and has vertices before accessing them
                if hasattr(path_line, 'vertices') and len(path_line.vertices) > 0:
                    # Fast breathing path parameters
                    fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
                    # Calculate hold phase parameters - using the entire usable width
                    hold_cycle_time = exhale_hold_duration + inhale_hold_duration
                    exhale_hold_segment = 14.5
                    inhale_transition = 0.5
                    inhale_hold_segment = 14.5
                    exhale_transition = 0.5
    
                    # Calculate segment widths using the entire usable width
                    exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                    inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                    inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                    exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
                    # Check the current phase and update path if needed
                    if is_hold_phase and len(path_line.vertices) == 3:
                        # Switch to hold phase path - spanning the entire usable width
                        path_line.vertices = [
                            [x_start, y_min],                                                # Start of exhale hold
                            [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                            [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                            [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                            [x_end, y_min]                                                   # End of exhale transition
                        ]
    
                    elif not is_hold_phase and len(path_line.vertices) == 5:
                        # Switch back to fast breathing path
                        path_line.vertices = [
                            [x_start, y_min],
                            [x_start + fast_inhale_width, y_max],
                            [x_end, y_min]
                        ]
    
            # Calculate current ball position
            x, y, phase = get_ball_position(t)
    
            # Update ball position
            ball.pos = [x, y]
    
            # Update background height and position
            background.height = y - y_min
            background.pos = [0, y_min + (y - y_min)/2]
    
            # Update phase text
            phase_text.text = phase
    
            # Update timer text - show remaining time
            time_left = int(total_duration - t)
            timer_text.text = f"Time remaining: {time_left}s"
    
            # Draw stimuli
            background.draw()
            path_line.draw()
            ball.draw()
            timer_text.draw()  # Keep timer text
    
            # Draw breath counter rectangles and round counter for fast_with_breath_hold mode
            if breath_protocol == "fast_with_breath_hold":
                # Draw round text (static "Round:" label)
                if round_text:
                    round_text.draw()
    
                # Draw breaths remaining text
                if breaths_remaining_text:
                    breaths_remaining_text.draw()
    
                # Update and draw breath rectangles
                if breath_rects:
                    # Calculate how many breaths have been completed in the current round
                    if round_time < fast_breathing_duration:
                        completed_breaths = int(round_time / fast_cycle_time)
                    else:
                        completed_breaths = num_fast_breaths  # All breaths completed
    
                    # Draw all breath rectangles - advance by 1 so current breath is already filled
                    for i, rect in enumerate(breath_rects):
                        # For the current breath cycle and completed ones
                        if i <= completed_breaths:  # Changed from < to <= to include current breath
                            # Fill current and completed breath rectangles with lightblue
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future breath rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
    
                # Update and draw round rectangles
                if round_rects:
                    # Draw all round rectangles
                    for i, rect in enumerate(round_rects):
                        if i <= round_num:  # Fill current and previous rounds
                            # Fill completed round rectangles with same blue as breath counter
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future round rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
            else:
                # For paced_breathing mode, still show the phase text
                phase_text.draw()
    
            # Check for quit (the Esc key)
            keys = event.getKeys(keyList=["escape"])
            if 'escape' in keys:
                continue_routine = False
                core.quit()  # This will exit the experiment
    
            # Refresh the screen
            win.flip()
    except Exception as e:
        print(f"Error in animation loop: {e}")
        # Handle the error appropriately for your experiment
    finally:
        # Clean up
        import gc
        gc.collect()  # Force garbage collection
    
    # Just clear the screen and wait
    try:
        win.flip()
        core.wait(2.0)
    except Exception as e:
        print(f"Error in final screen clear: {e}")
    
    # keep track of which components have finished
    _03_05_07_breathing_trialComponents = []
    for thisComponent in _03_05_07_breathing_trialComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_03_05_07_breathing_trial" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _03_05_07_breathing_trialComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_03_05_07_breathing_trial" ---
    for thisComponent in _03_05_07_breathing_trialComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_03_05_07_breathing_trial.stopped', globalClock.getTime())
    # the Routine "_03_05_07_breathing_trial" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_06_stroop_instr" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_06_stroop_instr.started', globalClock.getTime())
    block_resp.keys = []
    block_resp.rt = []
    _block_resp_allKeys = []
    # keep track of which components have finished
    _06_stroop_instrComponents = [stroop_instr, block_resp, instrText_4]
    for thisComponent in _06_stroop_instrComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_06_stroop_instr" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *stroop_instr* updates
        
        # if stroop_instr is starting this frame...
        if stroop_instr.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            stroop_instr.frameNStart = frameN  # exact frame index
            stroop_instr.tStart = t  # local t and not account for scr refresh
            stroop_instr.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(stroop_instr, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'stroop_instr.started')
            # update status
            stroop_instr.status = STARTED
            stroop_instr.setAutoDraw(True)
        
        # if stroop_instr is active this frame...
        if stroop_instr.status == STARTED:
            # update params
            pass
        
        # *block_resp* updates
        waitOnFlip = False
        
        # if block_resp is starting this frame...
        if block_resp.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            block_resp.frameNStart = frameN  # exact frame index
            block_resp.tStart = t  # local t and not account for scr refresh
            block_resp.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(block_resp, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'block_resp.started')
            # update status
            block_resp.status = STARTED
            # keyboard checking is just starting
            waitOnFlip = True
            win.callOnFlip(block_resp.clock.reset)  # t=0 on next screen flip
            win.callOnFlip(block_resp.clearEvents, eventType='keyboard')  # clear events on next screen flip
        if block_resp.status == STARTED and not waitOnFlip:
            theseKeys = block_resp.getKeys(keyList=['f1'], ignoreKeys=["escape"], waitRelease=False)
            _block_resp_allKeys.extend(theseKeys)
            if len(_block_resp_allKeys):
                block_resp.keys = _block_resp_allKeys[-1].name  # just the last key pressed
                block_resp.rt = _block_resp_allKeys[-1].rt
                block_resp.duration = _block_resp_allKeys[-1].duration
                # a response ends the routine
                continueRoutine = False
        
        # *instrText_4* updates
        
        # if instrText_4 is starting this frame...
        if instrText_4.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            instrText_4.frameNStart = frameN  # exact frame index
            instrText_4.tStart = t  # local t and not account for scr refresh
            instrText_4.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(instrText_4, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'instrText_4.started')
            # update status
            instrText_4.status = STARTED
            instrText_4.setAutoDraw(True)
        
        # if instrText_4 is active this frame...
        if instrText_4.status == STARTED:
            # update params
            pass
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _06_stroop_instrComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_06_stroop_instr" ---
    for thisComponent in _06_stroop_instrComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_06_stroop_instr.stopped', globalClock.getTime())
    # check responses
    if block_resp.keys in ['', [], None]:  # No response was made
        block_resp.keys = None
    thisExp.addData('block_resp.keys',block_resp.keys)
    if block_resp.keys != None:  # we had a response
        thisExp.addData('block_resp.rt', block_resp.rt)
        thisExp.addData('block_resp.duration', block_resp.duration)
    thisExp.nextEntry()
    # Run 'End Routine' code from t_begin_4
    outlet.push_sample(x=[1])
    # the Routine "_06_stroop_instr" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # set up handler to look after randomisation of conditions etc
    trials = data.TrialHandler(nReps=1.0, method='random', 
        extraInfo=expInfo, originPath=-1,
        trialList=data.importConditions('english.xlsx'),
        seed=None, name='trials')
    thisExp.addLoop(trials)  # add the loop to the experiment
    thisTrial = trials.trialList[0]  # so we can initialise stimuli with some values
    # abbreviate parameter names if possible (e.g. rgb = thisTrial.rgb)
    if thisTrial != None:
        for paramName in thisTrial:
            globals()[paramName] = thisTrial[paramName]
    
    for thisTrial in trials:
        currentLoop = trials
        thisExp.timestampOnFlip(win, 'thisRow.t')
        # pause experiment here if requested
        if thisExp.status == PAUSED:
            pauseExperiment(
                thisExp=thisExp, 
                inputs=inputs, 
                win=win, 
                timers=[routineTimer], 
                playbackComponents=[]
        )
        # abbreviate parameter names if possible (e.g. rgb = thisTrial.rgb)
        if thisTrial != None:
            for paramName in thisTrial:
                globals()[paramName] = thisTrial[paramName]
        
        # --- Prepare to start Routine "_06_stroop_trial" ---
        continueRoutine = True
        # update component parameters for each repeat
        thisExp.addData('_06_stroop_trial.started', globalClock.getTime())
        # Run 'Begin Routine' code from t_trial_stroop
        outlet.push_sample(x=[60])
        stim.setColor(wordColor, colorSpace='rgb')
        stim.setText(word)
        resp.keys = []
        resp.rt = []
        _resp_allKeys = []
        trial_counter.reset()
        trial_counter.setText('Trial ' + str(trials.thisN+1) +'/' +str(trials.nTotal))
        # keep track of which components have finished
        _06_stroop_trialComponents = [stim, resp, trial_counter, instrText_2]
        for thisComponent in _06_stroop_trialComponents:
            thisComponent.tStart = None
            thisComponent.tStop = None
            thisComponent.tStartRefresh = None
            thisComponent.tStopRefresh = None
            if hasattr(thisComponent, 'status'):
                thisComponent.status = NOT_STARTED
        # reset timers
        t = 0
        _timeToFirstFrame = win.getFutureFlipTime(clock="now")
        frameN = -1
        
        # --- Run Routine "_06_stroop_trial" ---
        routineForceEnded = not continueRoutine
        while continueRoutine and routineTimer.getTime() < 1.5:
            # get current time
            t = routineTimer.getTime()
            tThisFlip = win.getFutureFlipTime(clock=routineTimer)
            tThisFlipGlobal = win.getFutureFlipTime(clock=None)
            frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
            # update/draw components on each frame
            
            # *stim* updates
            
            # if stim is starting this frame...
            if stim.status == NOT_STARTED and tThisFlip >= 0.5-frameTolerance:
                # keep track of start time/frame for later
                stim.frameNStart = frameN  # exact frame index
                stim.tStart = t  # local t and not account for scr refresh
                stim.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(stim, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'stim.started')
                # update status
                stim.status = STARTED
                stim.setAutoDraw(True)
            
            # if stim is active this frame...
            if stim.status == STARTED:
                # update params
                pass
            
            # if stim is stopping this frame...
            if stim.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > stim.tStartRefresh + 1.0-frameTolerance:
                    # keep track of stop time/frame for later
                    stim.tStop = t  # not accounting for scr refresh
                    stim.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'stim.stopped')
                    # update status
                    stim.status = FINISHED
                    stim.setAutoDraw(False)
            
            # *resp* updates
            waitOnFlip = False
            
            # if resp is starting this frame...
            if resp.status == NOT_STARTED and tThisFlip >= .5-frameTolerance:
                # keep track of start time/frame for later
                resp.frameNStart = frameN  # exact frame index
                resp.tStart = t  # local t and not account for scr refresh
                resp.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(resp, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'resp.started')
                # update status
                resp.status = STARTED
                # keyboard checking is just starting
                waitOnFlip = True
                win.callOnFlip(resp.clock.reset)  # t=0 on next screen flip
                win.callOnFlip(resp.clearEvents, eventType='keyboard')  # clear events on next screen flip
            
            # if resp is stopping this frame...
            if resp.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > resp.tStartRefresh + 1.0-frameTolerance:
                    # keep track of stop time/frame for later
                    resp.tStop = t  # not accounting for scr refresh
                    resp.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'resp.stopped')
                    # update status
                    resp.status = FINISHED
                    resp.status = FINISHED
            if resp.status == STARTED and not waitOnFlip:
                theseKeys = resp.getKeys(keyList=['left','down','right'], ignoreKeys=["escape"], waitRelease=False)
                _resp_allKeys.extend(theseKeys)
                if len(_resp_allKeys):
                    resp.keys = _resp_allKeys[-1].name  # just the last key pressed
                    resp.rt = _resp_allKeys[-1].rt
                    resp.duration = _resp_allKeys[-1].duration
                    # was this correct?
                    if (resp.keys == str(corrAns)) or (resp.keys == corrAns):
                        resp.corr = 1
                    else:
                        resp.corr = 0
                    # a response ends the routine
                    continueRoutine = False
            
            # *trial_counter* updates
            
            # if trial_counter is starting this frame...
            if trial_counter.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
                # keep track of start time/frame for later
                trial_counter.frameNStart = frameN  # exact frame index
                trial_counter.tStart = t  # local t and not account for scr refresh
                trial_counter.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(trial_counter, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'trial_counter.started')
                # update status
                trial_counter.status = STARTED
                trial_counter.setAutoDraw(True)
            
            # if trial_counter is active this frame...
            if trial_counter.status == STARTED:
                # update params
                pass
            
            # if trial_counter is stopping this frame...
            if trial_counter.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > trial_counter.tStartRefresh + 1.5-frameTolerance:
                    # keep track of stop time/frame for later
                    trial_counter.tStop = t  # not accounting for scr refresh
                    trial_counter.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'trial_counter.stopped')
                    # update status
                    trial_counter.status = FINISHED
                    trial_counter.setAutoDraw(False)
            
            # *instrText_2* updates
            
            # if instrText_2 is starting this frame...
            if instrText_2.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
                # keep track of start time/frame for later
                instrText_2.frameNStart = frameN  # exact frame index
                instrText_2.tStart = t  # local t and not account for scr refresh
                instrText_2.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(instrText_2, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'instrText_2.started')
                # update status
                instrText_2.status = STARTED
                instrText_2.setAutoDraw(True)
            
            # if instrText_2 is active this frame...
            if instrText_2.status == STARTED:
                # update params
                pass
            
            # if instrText_2 is stopping this frame...
            if instrText_2.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > instrText_2.tStartRefresh + 1.5-frameTolerance:
                    # keep track of stop time/frame for later
                    instrText_2.tStop = t  # not accounting for scr refresh
                    instrText_2.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'instrText_2.stopped')
                    # update status
                    instrText_2.status = FINISHED
                    instrText_2.setAutoDraw(False)
            
            # check for quit (typically the Esc key)
            if defaultKeyboard.getKeys(keyList=["escape"]):
                thisExp.status = FINISHED
            if thisExp.status == FINISHED or endExpNow:
                endExperiment(thisExp, inputs=inputs, win=win)
                return
            
            # check if all components have finished
            if not continueRoutine:  # a component has requested a forced-end of Routine
                routineForceEnded = True
                break
            continueRoutine = False  # will revert to True if at least one component still running
            for thisComponent in _06_stroop_trialComponents:
                if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                    continueRoutine = True
                    break  # at least one component has not yet finished
            
            # refresh the screen
            if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
                win.flip()
        
        # --- Ending Routine "_06_stroop_trial" ---
        for thisComponent in _06_stroop_trialComponents:
            if hasattr(thisComponent, "setAutoDraw"):
                thisComponent.setAutoDraw(False)
        thisExp.addData('_06_stroop_trial.stopped', globalClock.getTime())
        # check responses
        if resp.keys in ['', [], None]:  # No response was made
            resp.keys = None
            # was no response the correct answer?!
            if str(corrAns).lower() == 'none':
               resp.corr = 1;  # correct non-response
            else:
               resp.corr = 0;  # failed to respond (incorrectly)
        # store data for trials (TrialHandler)
        trials.addData('resp.keys',resp.keys)
        trials.addData('resp.corr', resp.corr)
        if resp.keys != None:  # we had a response
            trials.addData('resp.rt', resp.rt)
            trials.addData('resp.duration', resp.duration)
        # using non-slip timing so subtract the expected duration of this Routine (unless ended on request)
        if routineForceEnded:
            routineTimer.reset()
        else:
            routineTimer.addTime(-1.500000)
        
        # --- Prepare to start Routine "_06_stroop_feedback" ---
        continueRoutine = True
        # update component parameters for each repeat
        thisExp.addData('_06_stroop_feedback.started', globalClock.getTime())
        # Run 'Begin Routine' code from code_2
        if resp.corr:
            fb = 'Correct!'
            fbcol = 'green'
        else:
            fb = 'Incorrect'
            fbcol = 'red'
            #wrong_sound.play()  # Play the wrong answer sound
            try:
                wrong_sound.stop()  # This is critical - must stop before playing again
                core.wait(0.01)     # Brief pause to let the stop take effect
                wrong_sound.play()  # Now play the sound
            except Exception as e:
                print(f"DEBUG: Error with sound: {e}")      
        # track accuracy for each condition by adding a 1 or 0 to a list
        english_accuracy.append(resp.corr)
        
        fbtxt.reset()
        fbtxt.setColor(fbcol, colorSpace='rgb')
        fbtxt.setText(fb)
        trial_counter_2.reset()
        trial_counter_2.setText('Trial ' + str(trials.thisN+1) +'/' +str(trials.nTotal))
        # keep track of which components have finished
        _06_stroop_feedbackComponents = [fbtxt, trial_counter_2, instrText_3]
        for thisComponent in _06_stroop_feedbackComponents:
            thisComponent.tStart = None
            thisComponent.tStop = None
            thisComponent.tStartRefresh = None
            thisComponent.tStopRefresh = None
            if hasattr(thisComponent, 'status'):
                thisComponent.status = NOT_STARTED
        # reset timers
        t = 0
        _timeToFirstFrame = win.getFutureFlipTime(clock="now")
        frameN = -1
        
        # --- Run Routine "_06_stroop_feedback" ---
        routineForceEnded = not continueRoutine
        while continueRoutine and routineTimer.getTime() < 0.5:
            # get current time
            t = routineTimer.getTime()
            tThisFlip = win.getFutureFlipTime(clock=routineTimer)
            tThisFlipGlobal = win.getFutureFlipTime(clock=None)
            frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
            # update/draw components on each frame
            
            # *fbtxt* updates
            
            # if fbtxt is starting this frame...
            if fbtxt.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
                # keep track of start time/frame for later
                fbtxt.frameNStart = frameN  # exact frame index
                fbtxt.tStart = t  # local t and not account for scr refresh
                fbtxt.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(fbtxt, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'fbtxt.started')
                # update status
                fbtxt.status = STARTED
                fbtxt.setAutoDraw(True)
            
            # if fbtxt is active this frame...
            if fbtxt.status == STARTED:
                # update params
                pass
            
            # if fbtxt is stopping this frame...
            if fbtxt.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > fbtxt.tStartRefresh + 0.5-frameTolerance:
                    # keep track of stop time/frame for later
                    fbtxt.tStop = t  # not accounting for scr refresh
                    fbtxt.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'fbtxt.stopped')
                    # update status
                    fbtxt.status = FINISHED
                    fbtxt.setAutoDraw(False)
            
            # *trial_counter_2* updates
            
            # if trial_counter_2 is starting this frame...
            if trial_counter_2.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
                # keep track of start time/frame for later
                trial_counter_2.frameNStart = frameN  # exact frame index
                trial_counter_2.tStart = t  # local t and not account for scr refresh
                trial_counter_2.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(trial_counter_2, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'trial_counter_2.started')
                # update status
                trial_counter_2.status = STARTED
                trial_counter_2.setAutoDraw(True)
            
            # if trial_counter_2 is active this frame...
            if trial_counter_2.status == STARTED:
                # update params
                pass
            
            # if trial_counter_2 is stopping this frame...
            if trial_counter_2.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > trial_counter_2.tStartRefresh + 0.5-frameTolerance:
                    # keep track of stop time/frame for later
                    trial_counter_2.tStop = t  # not accounting for scr refresh
                    trial_counter_2.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'trial_counter_2.stopped')
                    # update status
                    trial_counter_2.status = FINISHED
                    trial_counter_2.setAutoDraw(False)
            
            # *instrText_3* updates
            
            # if instrText_3 is starting this frame...
            if instrText_3.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
                # keep track of start time/frame for later
                instrText_3.frameNStart = frameN  # exact frame index
                instrText_3.tStart = t  # local t and not account for scr refresh
                instrText_3.tStartRefresh = tThisFlipGlobal  # on global time
                win.timeOnFlip(instrText_3, 'tStartRefresh')  # time at next scr refresh
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'instrText_3.started')
                # update status
                instrText_3.status = STARTED
                instrText_3.setAutoDraw(True)
            
            # if instrText_3 is active this frame...
            if instrText_3.status == STARTED:
                # update params
                pass
            
            # if instrText_3 is stopping this frame...
            if instrText_3.status == STARTED:
                # is it time to stop? (based on global clock, using actual start)
                if tThisFlipGlobal > instrText_3.tStartRefresh + 0.5-frameTolerance:
                    # keep track of stop time/frame for later
                    instrText_3.tStop = t  # not accounting for scr refresh
                    instrText_3.frameNStop = frameN  # exact frame index
                    # add timestamp to datafile
                    thisExp.timestampOnFlip(win, 'instrText_3.stopped')
                    # update status
                    instrText_3.status = FINISHED
                    instrText_3.setAutoDraw(False)
            
            # check for quit (typically the Esc key)
            if defaultKeyboard.getKeys(keyList=["escape"]):
                thisExp.status = FINISHED
            if thisExp.status == FINISHED or endExpNow:
                endExperiment(thisExp, inputs=inputs, win=win)
                return
            
            # check if all components have finished
            if not continueRoutine:  # a component has requested a forced-end of Routine
                routineForceEnded = True
                break
            continueRoutine = False  # will revert to True if at least one component still running
            for thisComponent in _06_stroop_feedbackComponents:
                if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                    continueRoutine = True
                    break  # at least one component has not yet finished
            
            # refresh the screen
            if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
                win.flip()
        
        # --- Ending Routine "_06_stroop_feedback" ---
        for thisComponent in _06_stroop_feedbackComponents:
            if hasattr(thisComponent, "setAutoDraw"):
                thisComponent.setAutoDraw(False)
        thisExp.addData('_06_stroop_feedback.stopped', globalClock.getTime())
        # using non-slip timing so subtract the expected duration of this Routine (unless ended on request)
        if routineForceEnded:
            routineTimer.reset()
        else:
            routineTimer.addTime(-0.500000)
        thisExp.nextEntry()
        
        if thisSession is not None:
            # if running in a Session with a Liaison client, send data up to now
            thisSession.sendExperimentData()
    # completed 1.0 repeats of 'trials'
    
    
    # --- Prepare to start Routine "_03_05_07_breathing_instr" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_03_05_07_breathing_instr.started', globalClock.getTime())
    key_resp_3.keys = []
    key_resp_3.rt = []
    _key_resp_3_allKeys = []
    # keep track of which components have finished
    _03_05_07_breathing_instrComponents = [text_3, key_resp_3]
    for thisComponent in _03_05_07_breathing_instrComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_03_05_07_breathing_instr" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *text_3* updates
        
        # if text_3 is starting this frame...
        if text_3.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            text_3.frameNStart = frameN  # exact frame index
            text_3.tStart = t  # local t and not account for scr refresh
            text_3.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(text_3, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'text_3.started')
            # update status
            text_3.status = STARTED
            text_3.setAutoDraw(True)
        
        # if text_3 is active this frame...
        if text_3.status == STARTED:
            # update params
            pass
        
        # *key_resp_3* updates
        waitOnFlip = False
        
        # if key_resp_3 is starting this frame...
        if key_resp_3.status == NOT_STARTED and tThisFlip >= 0-frameTolerance:
            # keep track of start time/frame for later
            key_resp_3.frameNStart = frameN  # exact frame index
            key_resp_3.tStart = t  # local t and not account for scr refresh
            key_resp_3.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(key_resp_3, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'key_resp_3.started')
            # update status
            key_resp_3.status = STARTED
            # keyboard checking is just starting
            waitOnFlip = True
            win.callOnFlip(key_resp_3.clock.reset)  # t=0 on next screen flip
            win.callOnFlip(key_resp_3.clearEvents, eventType='keyboard')  # clear events on next screen flip
        if key_resp_3.status == STARTED and not waitOnFlip:
            theseKeys = key_resp_3.getKeys(keyList=['f1'], ignoreKeys=["escape"], waitRelease=False)
            _key_resp_3_allKeys.extend(theseKeys)
            if len(_key_resp_3_allKeys):
                key_resp_3.keys = _key_resp_3_allKeys[-1].name  # just the last key pressed
                key_resp_3.rt = _key_resp_3_allKeys[-1].rt
                key_resp_3.duration = _key_resp_3_allKeys[-1].duration
                # a response ends the routine
                continueRoutine = False
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _03_05_07_breathing_instrComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_03_05_07_breathing_instr" ---
    for thisComponent in _03_05_07_breathing_instrComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_03_05_07_breathing_instr.stopped', globalClock.getTime())
    # check responses
    if key_resp_3.keys in ['', [], None]:  # No response was made
        key_resp_3.keys = None
    thisExp.addData('key_resp_3.keys',key_resp_3.keys)
    if key_resp_3.keys != None:  # we had a response
        thisExp.addData('key_resp_3.rt', key_resp_3.rt)
        thisExp.addData('key_resp_3.duration', key_resp_3.duration)
    thisExp.nextEntry()
    # Run 'End Routine' code from t_begin_3
    outlet.push_sample(x=[1])
    # the Routine "_03_05_07_breathing_instr" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_03_05_07_breathing_trial" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_03_05_07_breathing_trial.started', globalClock.getTime())
    # Run 'Begin Routine' code from t_stim_3
    outlet.push_sample(x=[int(31)])
    # Run 'Begin Routine' code from code_breathwork_vis
    # Paced Breathing Visualization for PsychoPy (height units)
    # To be pasted into a code component in a PsychoPy routine
    # Note: This code assumes 'win' (window object) is already defined in the PsychoPy environment
    # IDE warnings about undefined variables can be ignored as they will be defined in the PsychoPy environment
    
    from psychopy import visual, core, event
    import numpy as np
    
    # Check which breathing protocol to use (should be defined in experiment)
    # breath_protocol should be either "paced_breathing" or "fast_with_breath_hold"
    breath_protocol = "fast_with_breath_hold"  # Default value
    
    # Breathing settings based on protocol
    if breath_protocol == "paced_breathing":
        # Mode 1: Original paced breathing
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
        # Check if breath_duration is defined in the experiment
        try:
            # Try to access breath_duration
            total_duration = breath_duration  # Use variable from experiment
        except NameError:
            # If not defined, set a default value
            total_duration = 360  # Default to 6 minutes
            print("Warning: breath_duration not defined, using default: 300 seconds")
    else:  # fast_with_breath_hold
        # Mode 2: Fast breathing with holds
        # Each round: 30 rapid breaths + exhale hold + inhale hold
        fast_inhale_time = 1.5
        fast_exhale_time = 1.5
        fast_cycle_time = fast_inhale_time + fast_exhale_time
        num_fast_breaths = 30
        fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
        exhale_hold_duration = 15
        inhale_hold_duration = 15
        round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
        num_rounds = 3
    
        # Use breath_duration as hard cutoff, same as paced_breathing mode
        try:
            # Try to access breath_duration
            total_duration = breath_duration  # Use variable from experiment
        except NameError:
            # If not defined, set a default value
            total_duration = 360  # Default to 6 minutes
            print("Warning: breath_duration not defined, using default: 360 seconds")
    
        # Define these variables for the fast_with_breath_hold mode as well
        # to prevent "referenced before assignment" errors
        inhale_time = 4.5
        inhale_hold_time = 0.5
        exhale_time = 4.5
        exhale_hold_time = 0.5
        total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
    # Get window dimensions in height units
    # In height units, the height is always 1.0 (from -0.5 to 0.5)
    # The width depends on the aspect ratio
    win_height = 1.0
    win_width = win.size[0] / win.size[1]  # This gives us the aspect ratio
    
    # Normalize horizontal distance to window width
    # Leave some margin on both sides (10% of width)
    margin = 0.1 * win_width
    usable_width = win_width - 2 * margin
    x_start = -win_width/2 + margin
    x_end = win_width/2 - margin
    
    # Vertical parameters (in height units)
    y_min = -0.25  # Bottom position for exhale
    y_max = 0.25   # Top position for inhale
    y_range = y_max - y_min
    
    # Create the breathing path line segments
    # Calculate segment widths based on their duration
    if breath_protocol == "paced_breathing":
        # Original path vertices for paced breathing
        inhale_width = (inhale_time / total_cycle_time) * usable_width
        inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
        exhale_width = (exhale_time / total_cycle_time) * usable_width
        exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width
    
        vertices = [
            [x_start, y_min],                                  # Start point
            [x_start + inhale_width, y_max],                   # End of inhale
            [x_start + inhale_width + inhale_hold_width, y_max],  # End of inhale hold
            [x_start + inhale_width + inhale_hold_width + exhale_width, y_min],  # End of exhale
            [x_end, y_min]                                     # End of exhale hold
        ]
    else:
        # For fast_with_breath_hold, we need two different path visualizations:
        # 1. Fast breathing path (first 90 seconds of each round)
        # 2. Hold phases path (last 30 seconds of each round)
    
        # Calculate the current phase based on time
        current_time = 0  # This will be updated in the animation loop
        round_num = int(current_time / round_duration)
        round_time = current_time - (round_num * round_duration)
    
        # Fast breathing path parameters
        fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
        if round_time < fast_breathing_duration:
            # Fast breathing path - just up and down, no holds
            vertices = [
                [x_start, y_min],                      # Start point
                [x_start + fast_inhale_width, y_max],  # End of inhale
                [x_end, y_min]                         # End of exhale
            ]
        else:
            # Hold phases path - spanning the entire usable width
            # Define the segments of the hold cycle
            hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
            exhale_hold_segment = 14.5  # seconds
            inhale_transition = 0.5     # seconds
            inhale_hold_segment = 14.5  # seconds
            exhale_transition = 0.5     # seconds
    
            # Calculate segment widths based on the entire usable width
            exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
            inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
            exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
            vertices = [
                [x_start, y_min],                                                # Start of exhale hold
                [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                [x_end, y_min]                                                   # End of exhale transition
            ]
    
    # Create the path line
    path_line = visual.ShapeStim(
        win=win,
        vertices=vertices,
        closeShape=False,
        lineWidth=3,
        lineColor='white',
        opacity=0.7
    )
    
    # Create the ball that will travel along the path
    ball = visual.Circle(
        win=win,
        radius=0.015,  # Adjusted for height units
        fillColor='lightblue',
        lineColor='white',
        lineWidth=2
    )
    
    # Create background shading rectangle
    background = visual.Rect(
        win=win,
        width=win_width,
        height=y_range,
        fillColor='skyblue',
        opacity=0.2,
        pos=[0, y_min + y_range/2]  # Start at the bottom
    )
    
    # Function to calculate ball position at a given time
    def get_ball_position(current_time):
        if breath_protocol == "paced_breathing":
            # Define breathing parameters for paced breathing
            inhale_time = 4.5
            inhale_hold_time = 0.5
            exhale_time = 4.5
            exhale_hold_time = 0.5
            total_cycle_time = inhale_time + inhale_hold_time + exhale_time + exhale_hold_time
    
            # Original ball position calculation
            cycle_time = current_time % total_cycle_time
    
            # Calculate segment widths here to ensure they're defined
            inhale_width = (inhale_time / total_cycle_time) * usable_width
            inhale_hold_width = (inhale_hold_time / total_cycle_time) * usable_width
            exhale_width = (exhale_time / total_cycle_time) * usable_width
            exhale_hold_width = (exhale_hold_time / total_cycle_time) * usable_width
    
            # Determine which segment we're in
            if cycle_time < inhale_time:
                # Inhale segment - moving up
                progress = cycle_time / inhale_time
                x = x_start + progress * inhale_width
                y = y_min + progress * y_range
                phase = "Inhale"
    
            elif cycle_time < inhale_time + inhale_hold_time:
                # Inhale hold segment - staying at the top
                progress = (cycle_time - inhale_time) / inhale_hold_time
                x = x_start + inhale_width + progress * inhale_hold_width
                y = y_max
                phase = "Hold"
    
            elif cycle_time < inhale_time + inhale_hold_time + exhale_time:
                # Exhale segment - moving down
                progress = (cycle_time - inhale_time - inhale_hold_time) / exhale_time
                x = x_start + inhale_width + inhale_hold_width + progress * exhale_width
                y = y_max - progress * y_range
                phase = "Exhale"
    
            else:
                # Exhale hold segment - staying at the bottom
                progress = (cycle_time - inhale_time - inhale_hold_time - exhale_time) / exhale_hold_time
                x = x_start + inhale_width + inhale_hold_width + exhale_width + progress * exhale_hold_width
                y = y_min
                phase = "Hold"
    
        else:  # fast_with_breath_hold
            # Define breathing parameters for fast breathing with holds
            fast_inhale_time = 1.5
            fast_exhale_time = 1.5
            fast_cycle_time = fast_inhale_time + fast_exhale_time
            num_fast_breaths = 30
            fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
            exhale_hold_duration = 15
            inhale_hold_duration = 15
            round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
            num_rounds = 3
    
            # Calculate which round we're in
            round_num = int(current_time / round_duration)
            if round_num >= num_rounds:
                round_num = num_rounds - 1
    
            # Calculate time within the current round
            round_time = current_time - (round_num * round_duration)
    
            # Determine which phase we're in
            if round_time < fast_breathing_duration:
                # Fast breathing phase
                cycle_num = int(round_time / fast_cycle_time)
                cycle_time = round_time % fast_cycle_time
    
                # Fast breathing path parameters - ensure consistent with path creation
                fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
                if cycle_time < fast_inhale_time:
                    # Inhale segment - moving up
                    progress = cycle_time / fast_inhale_time
                    x = x_start + progress * fast_inhale_width
                    y = y_min + progress * y_range
                    phase = "Inhale"
                else:
                    # Exhale segment - moving down
                    progress = (cycle_time - fast_inhale_time) / fast_exhale_time
                    # Use the remaining width to ensure ball reaches end point
                    remaining_width = usable_width - fast_inhale_width
                    x = x_start + fast_inhale_width + progress * remaining_width
                    y = y_max - progress * y_range
                    phase = "Exhale"
    
                # Add breath counter to phase
                phase += f" ({cycle_num+1}/{num_fast_breaths})"
    
            else:
                # Hold phases (combined exhale hold and inhale hold)
                hold_time = round_time - fast_breathing_duration
    
                # Calculate position in the hold cycle
                hold_cycle_time = exhale_hold_duration + inhale_hold_duration  # 30 seconds total
    
                # Define the segments of the hold cycle - ensure consistent with path creation
                exhale_hold_segment = 14.5  # seconds
                inhale_transition = 0.5     # seconds
                inhale_hold_segment = 14.5  # seconds
                exhale_transition = 0.5     # seconds
    
                # Calculate segment widths using the entire usable width - ensure consistent with path creation
                exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
                # Determine which segment of the hold cycle we're in
                if hold_time < exhale_hold_segment:
                    # Exhale hold (bottom)
                    progress = hold_time / exhale_hold_segment
                    x = x_start + progress * exhale_hold_width
                    y = y_min
                    phase = f"Exhaled Hold ({int(exhale_hold_segment - hold_time)}s)"
    
                elif hold_time < exhale_hold_segment + inhale_transition:
                    # Quick inhale transition (moving up)
                    progress = (hold_time - exhale_hold_segment) / inhale_transition
                    x = x_start + exhale_hold_width + progress * inhale_trans_width
                    y = y_min + progress * y_range
                    phase = "Inhale"
    
                elif hold_time < exhale_hold_segment + inhale_transition + inhale_hold_segment:
                    # Inhale hold (top)
                    progress = (hold_time - exhale_hold_segment - inhale_transition) / inhale_hold_segment
                    x = x_start + exhale_hold_width + inhale_trans_width + progress * inhale_hold_width
                    y = y_max
                    phase = f"Inhaled Hold ({int(inhale_hold_segment - (progress * inhale_hold_segment))}s)"
    
                else:
                    # Quick exhale transition (moving down)
                    progress = (hold_time - exhale_hold_segment - inhale_transition - inhale_hold_segment) / exhale_transition
                    x = x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width + progress * exhale_trans_width
                    y = y_max - progress * y_range
                    phase = "Exhale"
    
            # Add round information
            phase = f"Round {round_num+1}/{num_rounds}: " + phase
    
        return x, y, phase
    
    # Create text display for the breathing phase
    phase_text = visual.TextStim(
        win=win,
        text="Inhale",
        pos=[0, -0.35],
        color='white',
        height=0.05  # Adjusted for height units
    )
    
    # Create a timer display
    timer_text = visual.TextStim(
        win=win,
        text="",
        pos=[0, 0.4],
        color='white',
        height=0.03  # Adjusted for height units
    )
    
    # Create breath counter rectangles for fast_with_breath_hold mode
    breath_rects = []
    round_rects = []
    if breath_protocol == "fast_with_breath_hold":
        # Calculate rectangle dimensions and positions
        rect_height = 0.05  # Height of each rectangle
        rect_width = 0.02   # Width of each rectangle
        rect_spacing = 0.005  # Space between rectangles
    
        # Position in right 2/3 of screen
        screen_left = -win_width/2
        screen_right = win_width/2
        left_third_end = screen_left + win_width/3
    
        # Calculate total width needed for all breath rectangles
        total_rect_width = num_fast_breaths * (rect_width + rect_spacing) - rect_spacing
    
        # Center the rectangles in the right 2/3 of the screen
        rect_area_start = left_third_end
        rect_area_end = screen_right
        rect_area_width = rect_area_end - rect_area_start
    
        # Start position for the first rectangle - leave space for the "Breaths:" text
        rect_start_x = left_third_end + 0.25  # Adjusted to leave space for text
    
        # Create all breath rectangles - initially gray with white outline
        for i in range(num_fast_breaths):
            rect = visual.Rect(
                win=win,
                width=rect_width,
                height=rect_height,
                fillColor='gray',  # Start with gray fill
                lineColor='white',
                lineWidth=1,
                opacity=0.3,  # Make the gray semi-transparent
                pos=[rect_start_x + i * (rect_width + rect_spacing), -0.4]
            )
            breath_rects.append(rect)
    
        # Create round counter rectangles
        # Calculate dimensions for round rectangles (slightly larger)
        round_rect_height = 0.06
        round_rect_width = 0.03
        round_rect_spacing = 0.01
    
        # Position round rectangles in the left 1/4 of the screen
        left_quarter_end = screen_left + win_width/4
        round_rect_start_x = left_quarter_end - 0.15  # Positioned to the left of the round text
    
        # Create round rectangles - initially gray with white outline
        for i in range(num_rounds):
            rect = visual.Rect(
                win=win,
                width=round_rect_width,
                height=round_rect_height,
                fillColor='gray',  # Start with gray fill
                lineColor='white',
                lineWidth=1,
                opacity=0.3,  # Make the gray semi-transparent
                pos=[round_rect_start_x + i * (round_rect_width + round_rect_spacing), -0.4]
            )
            round_rects.append(rect)
    
    # Create round counter text for fast_with_breath_hold mode
    round_text = None
    breaths_remaining_text = None
    if breath_protocol == "fast_with_breath_hold":
        # Round counter text - positioned at the right side of the left 1/4
        left_quarter_end = screen_left + win_width/4
        round_text = visual.TextStim(
            win=win,
            text="Round:",
            pos=[left_quarter_end - 0.35, -0.4],  # Moved further left so text ends ~2 chars before previous start
            color='white',
            height=0.05,  # Adjusted for height units
            alignHoriz='left'  # Align text to the left for consistent positioning
        )
    
        # Breaths remaining text - positioned before the rectangles
        breaths_remaining_text = visual.TextStim(
            win=win,
            text="Breaths:",
            pos=[left_third_end + 0.05, -0.4],  # Left side of right 2/3, moved left
            color='white',
            height=0.05,  # Adjusted for height units
            alignHoriz='left'
        )
    
    # Main animation loop
    timer = core.Clock()
    continue_routine = True
    
    try:
        while continue_routine:
            # Get current time
            t = timer.getTime()
    
            # Check if we've reached the total duration
            if t >= total_duration:
                continue_routine = False
                break
    
            # For fast_with_breath_hold mode, update the path visualization based on the current phase
            if breath_protocol == "fast_with_breath_hold":
                # Define these parameters inside the loop to ensure they're available
                fast_inhale_time = 1.5
                fast_exhale_time = 1.5
                fast_cycle_time = fast_inhale_time + fast_exhale_time
                num_fast_breaths = 30
                fast_breathing_duration = num_fast_breaths * fast_cycle_time  # 90 seconds
                exhale_hold_duration = 15
                inhale_hold_duration = 15
                round_duration = fast_breathing_duration + exhale_hold_duration + inhale_hold_duration  # 120 seconds
                num_rounds = 3
    
                round_num = int(t / round_duration)
                if round_num >= num_rounds:
                    round_num = num_rounds - 1
    
                round_time = t - (round_num * round_duration)
    
                # Check if we need to switch path visualization
                is_hold_phase = round_time >= fast_breathing_duration
    
                # Check if path_line exists and has vertices before accessing them
                if hasattr(path_line, 'vertices') and len(path_line.vertices) > 0:
                    # Fast breathing path parameters
                    fast_inhale_width = (fast_inhale_time / fast_cycle_time) * usable_width
    
                    # Calculate hold phase parameters - using the entire usable width
                    hold_cycle_time = exhale_hold_duration + inhale_hold_duration
                    exhale_hold_segment = 14.5
                    inhale_transition = 0.5
                    inhale_hold_segment = 14.5
                    exhale_transition = 0.5
    
                    # Calculate segment widths using the entire usable width
                    exhale_hold_width = (exhale_hold_segment / hold_cycle_time) * usable_width
                    inhale_trans_width = (inhale_transition / hold_cycle_time) * usable_width
                    inhale_hold_width = (inhale_hold_segment / hold_cycle_time) * usable_width
                    exhale_trans_width = (exhale_transition / hold_cycle_time) * usable_width
    
                    # Check the current phase and update path if needed
                    if is_hold_phase and len(path_line.vertices) == 3:
                        # Switch to hold phase path - spanning the entire usable width
                        path_line.vertices = [
                            [x_start, y_min],                                                # Start of exhale hold
                            [x_start + exhale_hold_width, y_min],                            # End of exhale hold
                            [x_start + exhale_hold_width + inhale_trans_width, y_max],       # End of inhale transition
                            [x_start + exhale_hold_width + inhale_trans_width + inhale_hold_width, y_max],  # End of inhale hold
                            [x_end, y_min]                                                   # End of exhale transition
                        ]
    
                    elif not is_hold_phase and len(path_line.vertices) == 5:
                        # Switch back to fast breathing path
                        path_line.vertices = [
                            [x_start, y_min],
                            [x_start + fast_inhale_width, y_max],
                            [x_end, y_min]
                        ]
    
            # Calculate current ball position
            x, y, phase = get_ball_position(t)
    
            # Update ball position
            ball.pos = [x, y]
    
            # Update background height and position
            background.height = y - y_min
            background.pos = [0, y_min + (y - y_min)/2]
    
            # Update phase text
            phase_text.text = phase
    
            # Update timer text - show remaining time
            time_left = int(total_duration - t)
            timer_text.text = f"Time remaining: {time_left}s"
    
            # Draw stimuli
            background.draw()
            path_line.draw()
            ball.draw()
            timer_text.draw()  # Keep timer text
    
            # Draw breath counter rectangles and round counter for fast_with_breath_hold mode
            if breath_protocol == "fast_with_breath_hold":
                # Draw round text (static "Round:" label)
                if round_text:
                    round_text.draw()
    
                # Draw breaths remaining text
                if breaths_remaining_text:
                    breaths_remaining_text.draw()
    
                # Update and draw breath rectangles
                if breath_rects:
                    # Calculate how many breaths have been completed in the current round
                    if round_time < fast_breathing_duration:
                        completed_breaths = int(round_time / fast_cycle_time)
                    else:
                        completed_breaths = num_fast_breaths  # All breaths completed
    
                    # Draw all breath rectangles - advance by 1 so current breath is already filled
                    for i, rect in enumerate(breath_rects):
                        # For the current breath cycle and completed ones
                        if i <= completed_breaths:  # Changed from < to <= to include current breath
                            # Fill current and completed breath rectangles with lightblue
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future breath rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
    
                # Update and draw round rectangles
                if round_rects:
                    # Draw all round rectangles
                    for i, rect in enumerate(round_rects):
                        if i <= round_num:  # Fill current and previous rounds
                            # Fill completed round rectangles with same blue as breath counter
                            rect.fillColor = 'lightblue'
                            rect.opacity = 1.0
                        else:
                            # Keep future round rectangles gray and semi-transparent
                            rect.fillColor = 'gray'
                            rect.opacity = 0.3
                        rect.draw()
            else:
                # For paced_breathing mode, still show the phase text
                phase_text.draw()
    
            # Check for quit (the Esc key)
            keys = event.getKeys(keyList=["escape"])
            if 'escape' in keys:
                continue_routine = False
                core.quit()  # This will exit the experiment
    
            # Refresh the screen
            win.flip()
    except Exception as e:
        print(f"Error in animation loop: {e}")
        # Handle the error appropriately for your experiment
    finally:
        # Clean up
        import gc
        gc.collect()  # Force garbage collection
    
    # Just clear the screen and wait
    try:
        win.flip()
        core.wait(2.0)
    except Exception as e:
        print(f"Error in final screen clear: {e}")
    
    # keep track of which components have finished
    _03_05_07_breathing_trialComponents = []
    for thisComponent in _03_05_07_breathing_trialComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_03_05_07_breathing_trial" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _03_05_07_breathing_trialComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_03_05_07_breathing_trial" ---
    for thisComponent in _03_05_07_breathing_trialComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_03_05_07_breathing_trial.stopped', globalClock.getTime())
    # the Routine "_03_05_07_breathing_trial" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "_01_08_rest_state_trial" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('_01_08_rest_state_trial.started', globalClock.getTime())
    # Run 'Begin Routine' code from t_stim
    outlet.push_sample(x=[10])
    # keep track of which components have finished
    _01_08_rest_state_trialComponents = [cross]
    for thisComponent in _01_08_rest_state_trialComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "_01_08_rest_state_trial" ---
    routineForceEnded = not continueRoutine
    while continueRoutine:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *cross* updates
        
        # if cross is starting this frame...
        if cross.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            cross.frameNStart = frameN  # exact frame index
            cross.tStart = t  # local t and not account for scr refresh
            cross.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(cross, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'cross.started')
            # update status
            cross.status = STARTED
            cross.setAutoDraw(True)
        
        # if cross is active this frame...
        if cross.status == STARTED:
            # update params
            pass
        
        # if cross is stopping this frame...
        if cross.status == STARTED:
            # is it time to stop? (based on global clock, using actual start)
            if tThisFlipGlobal > cross.tStartRefresh + breath_duration-frameTolerance:
                # keep track of stop time/frame for later
                cross.tStop = t  # not accounting for scr refresh
                cross.frameNStop = frameN  # exact frame index
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'cross.stopped')
                # update status
                cross.status = FINISHED
                cross.setAutoDraw(False)
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in _01_08_rest_state_trialComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "_01_08_rest_state_trial" ---
    for thisComponent in _01_08_rest_state_trialComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('_01_08_rest_state_trial.stopped', globalClock.getTime())
    # the Routine "_01_08_rest_state_trial" was not non-slip safe, so reset the non-slip timer
    routineTimer.reset()
    
    # --- Prepare to start Routine "End" ---
    continueRoutine = True
    # update component parameters for each repeat
    thisExp.addData('End.started', globalClock.getTime())
    # Run 'Begin Routine' code from t_end
    outlet.push_sample(x=[2])
    
    #Clean up
    import gc
    gc.collect()  # Force garbage collection
    
    # Just clear the screen and wait
    win.flip()
    core.wait(2.0)
    # keep track of which components have finished
    EndComponents = [EndMessage]
    for thisComponent in EndComponents:
        thisComponent.tStart = None
        thisComponent.tStop = None
        thisComponent.tStartRefresh = None
        thisComponent.tStopRefresh = None
        if hasattr(thisComponent, 'status'):
            thisComponent.status = NOT_STARTED
    # reset timers
    t = 0
    _timeToFirstFrame = win.getFutureFlipTime(clock="now")
    frameN = -1
    
    # --- Run Routine "End" ---
    routineForceEnded = not continueRoutine
    while continueRoutine and routineTimer.getTime() < 1.0:
        # get current time
        t = routineTimer.getTime()
        tThisFlip = win.getFutureFlipTime(clock=routineTimer)
        tThisFlipGlobal = win.getFutureFlipTime(clock=None)
        frameN = frameN + 1  # number of completed frames (so 0 is the first frame)
        # update/draw components on each frame
        
        # *EndMessage* updates
        
        # if EndMessage is starting this frame...
        if EndMessage.status == NOT_STARTED and tThisFlip >= 0.0-frameTolerance:
            # keep track of start time/frame for later
            EndMessage.frameNStart = frameN  # exact frame index
            EndMessage.tStart = t  # local t and not account for scr refresh
            EndMessage.tStartRefresh = tThisFlipGlobal  # on global time
            win.timeOnFlip(EndMessage, 'tStartRefresh')  # time at next scr refresh
            # add timestamp to datafile
            thisExp.timestampOnFlip(win, 'EndMessage.started')
            # update status
            EndMessage.status = STARTED
            EndMessage.setAutoDraw(True)
        
        # if EndMessage is active this frame...
        if EndMessage.status == STARTED:
            # update params
            pass
        
        # if EndMessage is stopping this frame...
        if EndMessage.status == STARTED:
            # is it time to stop? (based on global clock, using actual start)
            if tThisFlipGlobal > EndMessage.tStartRefresh + 1.0-frameTolerance:
                # keep track of stop time/frame for later
                EndMessage.tStop = t  # not accounting for scr refresh
                EndMessage.frameNStop = frameN  # exact frame index
                # add timestamp to datafile
                thisExp.timestampOnFlip(win, 'EndMessage.stopped')
                # update status
                EndMessage.status = FINISHED
                EndMessage.setAutoDraw(False)
        
        # check for quit (typically the Esc key)
        if defaultKeyboard.getKeys(keyList=["escape"]):
            thisExp.status = FINISHED
        if thisExp.status == FINISHED or endExpNow:
            endExperiment(thisExp, inputs=inputs, win=win)
            return
        
        # check if all components have finished
        if not continueRoutine:  # a component has requested a forced-end of Routine
            routineForceEnded = True
            break
        continueRoutine = False  # will revert to True if at least one component still running
        for thisComponent in EndComponents:
            if hasattr(thisComponent, "status") and thisComponent.status != FINISHED:
                continueRoutine = True
                break  # at least one component has not yet finished
        
        # refresh the screen
        if continueRoutine:  # don't flip if this routine is over or we'll get a blank screen
            win.flip()
    
    # --- Ending Routine "End" ---
    for thisComponent in EndComponents:
        if hasattr(thisComponent, "setAutoDraw"):
            thisComponent.setAutoDraw(False)
    thisExp.addData('End.stopped', globalClock.getTime())
    # using non-slip timing so subtract the expected duration of this Routine (unless ended on request)
    if routineForceEnded:
        routineTimer.reset()
    else:
        routineTimer.addTime(-1.000000)
    
    
    
    # mark experiment as finished
    endExperiment(thisExp, win=win, inputs=inputs)


def saveData(thisExp):
    """
    Save data from this experiment
    
    Parameters
    ==========
    thisExp : psychopy.data.ExperimentHandler
        Handler object for this experiment, contains the data to save and information about 
        where to save it to.
    """
    filename = thisExp.dataFileName
    # these shouldn't be strictly necessary (should auto-save)
    thisExp.saveAsWideText(filename + '.csv', delim='auto')
    thisExp.saveAsPickle(filename)


def endExperiment(thisExp, inputs=None, win=None):
    """
    End this experiment, performing final shut down operations.
    
    This function does NOT close the window or end the Python process - use `quit` for this.
    
    Parameters
    ==========
    thisExp : psychopy.data.ExperimentHandler
        Handler object for this experiment, contains the data to save and information about 
        where to save it to.
    inputs : dict
        Dictionary of input devices by name.
    win : psychopy.visual.Window
        Window for this experiment.
    """
    if win is not None:
        # remove autodraw from all current components
        win.clearAutoDraw()
        # Flip one final time so any remaining win.callOnFlip() 
        # and win.timeOnFlip() tasks get executed
        win.flip()
    # mark experiment handler as finished
    thisExp.status = FINISHED
    # shut down eyetracker, if there is one
    if inputs is not None:
        if 'eyetracker' in inputs and inputs['eyetracker'] is not None:
            inputs['eyetracker'].setConnectionState(False)
    logging.flush()


def quit(thisExp, win=None, inputs=None, thisSession=None):
    """
    Fully quit, closing the window and ending the Python process.
    
    Parameters
    ==========
    win : psychopy.visual.Window
        Window to close.
    inputs : dict
        Dictionary of input devices by name.
    thisSession : psychopy.session.Session or None
        Handle of the Session object this experiment is being run from, if any.
    """
    thisExp.abort()  # or data files will save again on exit
    # make sure everything is closed down
    if win is not None:
        # Flip one final time so any remaining win.callOnFlip() 
        # and win.timeOnFlip() tasks get executed before quitting
        win.flip()
        win.close()
    if inputs is not None:
        if 'eyetracker' in inputs and inputs['eyetracker'] is not None:
            inputs['eyetracker'].setConnectionState(False)
    logging.flush()
    if thisSession is not None:
        thisSession.stop()
    # terminate Python process
    core.quit()


# if running this experiment as a script...
if __name__ == '__main__':
    # call all functions in order
    expInfo = showExpInfoDlg(expInfo=expInfo)
    thisExp = setupData(expInfo=expInfo)
    logFile = setupLogging(filename=thisExp.dataFileName)
    win = setupWindow(expInfo=expInfo)
    inputs = setupInputs(expInfo=expInfo, thisExp=thisExp, win=win)
    run(
        expInfo=expInfo, 
        thisExp=thisExp, 
        win=win, 
        inputs=inputs
    )
    saveData(thisExp=thisExp)
    quit(thisExp=thisExp, win=win, inputs=inputs)
